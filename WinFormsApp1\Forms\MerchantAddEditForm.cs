using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل التجار
    /// </summary>
    public partial class MerchantAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة تاجر جديد
        /// </summary>
        public MerchantAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🏢 إدارة التجار";

                _currentEntity = new Merchant();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة التاجر:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل تاجر موجود
        /// </summary>
        public MerchantAddEditForm(Merchant selectedMerchant)
        {
            try
            {
                InitializeComponent();
                this.Text = "🏢 إدارة التجار";

                _currentEntity = selectedMerchant;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل التاجر:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var merchants = await _context!.Merchants
                    .Include(m => m.CreatedByUser)
                    .Include(m => m.ModifiedByUser)
                    .OrderBy(m => m.Name)
                    .ToListAsync();

                return merchants.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل التجار من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Merchant
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Merchant merchant)
            {
                txtName.Text = merchant.Name ?? "";
                memoContactDetails.Text = merchant.ContactDetails ?? "";

                // عرض تاريخ الإنشاء
                if (merchant.MerchantId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {merchant.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Merchant merchant)
            {
                merchant.Name = txtName.Text.Trim();
                merchant.ContactDetails = string.IsNullOrWhiteSpace(memoContactDetails.Text) ? null : memoContactDetails.Text.Trim();

                // تعيين تاريخ الإنشاء للتجار الجدد
                if (merchant.MerchantId == 0)
                {
                    merchant.CreatedAt = DateTime.Now;
                }
                else
                {
                    merchant.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم التاجر
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم التاجر", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول اسم التاجر
            if (txtName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم التاجر لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول تفاصيل الاتصال
            if (!string.IsNullOrWhiteSpace(memoContactDetails.Text) && memoContactDetails.Text.Trim().Length > 500)
            {
                XtraMessageBox.Show("تفاصيل الاتصال لا يجب أن تتجاوز 500 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                memoContactDetails.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم التاجر
            if (_currentEntity is Merchant currentMerchant)
            {
                var existingMerchant = _context!.Merchants.FirstOrDefault(m =>
                    m.Name == txtName.Text.Trim() && m.MerchantId != currentMerchant.MerchantId);

                if (existingMerchant != null)
                {
                    XtraMessageBox.Show("اسم التاجر موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// حفظ التاجر في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Merchant merchant)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        merchant.CreatedAt = DateTime.Now;
                        merchant.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Merchants.Add(merchant);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingMerchant = await _context!.Merchants.FindAsync(merchant.MerchantId);
                        if (existingMerchant != null)
                        {
                            existingMerchant.Name = merchant.Name;
                            existingMerchant.ContactDetails = merchant.ContactDetails;
                            existingMerchant.ModifiedAt = DateTime.Now;
                            existingMerchant.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Merchants.Update(existingMerchant);
                        }
                        else
                        {
                            return false; // التاجر غير موجود
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ التاجر");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف التاجر من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Merchant merchant)
            {
                try
                {
                    // البحث عن التاجر في قاعدة البيانات للتأكد من وجوده
                    var existingMerchant = await _context!.Merchants.FindAsync(merchant.MerchantId);
                    if (existingMerchant != null)
                    {
                        _context.Merchants.Remove(existingMerchant);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("التاجر غير موجود"), "حذف التاجر");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف التاجر");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Merchant merchant)
            {
                return $"هل أنت متأكد من حذف التاجر '{merchant.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذا التاجر؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث بيانات التاجر بنجاح" : "تم إضافة التاجر الجديد بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Merchant merchant)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "🏢 إضافة تاجر جديد",
                    FormMode.Edit => $"🏢 تعديل التاجر: {merchant.Name}",
                    FormMode.View => $"🏢 عرض التاجر: {merchant.Name}",
                    _ => "🏢 إدارة التجار"
                };
            }
            else
            {
                this.Text = "🏢 إدارة التجار";
            }
        }

        #endregion
    }
}
