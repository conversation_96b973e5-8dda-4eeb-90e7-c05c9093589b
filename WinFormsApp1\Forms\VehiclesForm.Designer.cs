namespace WinFormsApp1.Forms
{
    partial class VehiclesForm
    {
        private System.ComponentModel.IContainer components = null;

        private void InitializeComponent()
        {
            gridControl1 = new DevExpress.XtraGrid.GridControl();
            gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            colVehicleId = new DevExpress.XtraGrid.Columns.GridColumn();
            colVehicleName = new DevExpress.XtraGrid.Columns.GridColumn();
            colTransportCompanyName = new DevExpress.XtraGrid.Columns.GridColumn();
            colVehicleTypeName = new DevExpress.XtraGrid.Columns.GridColumn();
            colDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            colCreatedAt = new DevExpress.XtraGrid.Columns.GridColumn();
            panelControl1 = new DevExpress.XtraEditors.PanelControl();
            panelControl2 = new DevExpress.XtraEditors.PanelControl();
            btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            btnExportToExcel = new DevExpress.XtraEditors.SimpleButton();
            btnPrint = new DevExpress.XtraEditors.SimpleButton();
            btnView = new DevExpress.XtraEditors.SimpleButton();
            btnNew = new DevExpress.XtraEditors.SimpleButton();
            labelControl1 = new DevExpress.XtraEditors.LabelControl();
            panelControl3 = new DevExpress.XtraEditors.PanelControl();
            txtSearch = new DevExpress.XtraEditors.TextEdit();
            lblSearch = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)gridControl1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).BeginInit();
            panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl2).BeginInit();
            panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl3).BeginInit();
            panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).BeginInit();
            SuspendLayout();

            gridControl1.Dock = DockStyle.Fill;
            gridControl1.Location = new Point(0, 108);
            gridControl1.MainView = gridView1;
            gridControl1.Name = "gridControl1";
            gridControl1.Size = new Size(1200, 492);
            gridControl1.TabIndex = 0;
            gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] { gridView1 });

            gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] { colVehicleId, colVehicleName, colTransportCompanyName, colVehicleTypeName, colDescription, colCreatedAt });
            gridView1.GridControl = gridControl1;
            gridView1.Name = "gridView1";
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsSelection.MultiSelect = false;
            gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;

            colVehicleId.Caption = "المعرف";
            colVehicleId.FieldName = "VehicleId";
            colVehicleId.Name = "colVehicleId";
            colVehicleId.Visible = true;
            colVehicleId.VisibleIndex = 0;
            colVehicleId.Width = 80;

            colVehicleName.Caption = "اسم آلية النقل";
            colVehicleName.FieldName = "VehicleName";
            colVehicleName.Name = "colVehicleName";
            colVehicleName.Visible = true;
            colVehicleName.VisibleIndex = 1;
            colVehicleName.Width = 250;

            colTransportCompanyName.Caption = "شركة النقل";
            colTransportCompanyName.FieldName = "TransportCompany.Name";
            colTransportCompanyName.Name = "colTransportCompanyName";
            colTransportCompanyName.Visible = true;
            colTransportCompanyName.VisibleIndex = 2;
            colTransportCompanyName.Width = 200;

            colVehicleTypeName.Caption = "نوع الآلية";
            colVehicleTypeName.FieldName = "VehicleType.VehicleTypeName";
            colVehicleTypeName.Name = "colVehicleTypeName";
            colVehicleTypeName.Visible = true;
            colVehicleTypeName.VisibleIndex = 3;
            colVehicleTypeName.Width = 150;

            colDescription.Caption = "الوصف";
            colDescription.FieldName = "Description";
            colDescription.Name = "colDescription";
            colDescription.Visible = true;
            colDescription.VisibleIndex = 4;
            colDescription.Width = 300;

            colCreatedAt.Caption = "تاريخ الإنشاء";
            colCreatedAt.FieldName = "CreatedAt";
            colCreatedAt.Name = "colCreatedAt";
            colCreatedAt.Visible = true;
            colCreatedAt.VisibleIndex = 5;
            colCreatedAt.Width = 120;

            panelControl1.Controls.Add(panelControl2);
            panelControl1.Controls.Add(panelControl3);
            panelControl1.Dock = DockStyle.Top;
            panelControl1.Location = new Point(0, 0);
            panelControl1.Name = "panelControl1";
            panelControl1.Size = new Size(1200, 108);
            panelControl1.TabIndex = 1;

            panelControl2.Controls.Add(btnRefresh);
            panelControl2.Controls.Add(btnExportToExcel);
            panelControl2.Controls.Add(btnPrint);
            panelControl2.Controls.Add(btnView);
            panelControl2.Controls.Add(btnNew);
            panelControl2.Controls.Add(labelControl1);
            panelControl2.Dock = DockStyle.Top;
            panelControl2.Location = new Point(2, 2);
            panelControl2.Name = "panelControl2";
            panelControl2.Size = new Size(1196, 54);
            panelControl2.TabIndex = 0;

            btnRefresh.Appearance.BackColor = Color.FromArgb(255, 193, 7);
            btnRefresh.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnRefresh.Appearance.ForeColor = Color.Black;
            btnRefresh.Appearance.Options.UseBackColor = true;
            btnRefresh.Appearance.Options.UseFont = true;
            btnRefresh.Appearance.Options.UseForeColor = true;
            btnRefresh.Location = new Point(12, 12);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(90, 30);
            btnRefresh.TabIndex = 5;
            btnRefresh.Text = "🔄 تحديث";

            btnExportToExcel.Appearance.BackColor = Color.FromArgb(40, 167, 69);
            btnExportToExcel.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnExportToExcel.Appearance.ForeColor = Color.White;
            btnExportToExcel.Appearance.Options.UseBackColor = true;
            btnExportToExcel.Appearance.Options.UseFont = true;
            btnExportToExcel.Appearance.Options.UseForeColor = true;
            btnExportToExcel.Location = new Point(108, 12);
            btnExportToExcel.Name = "btnExportToExcel";
            btnExportToExcel.Size = new Size(120, 30);
            btnExportToExcel.TabIndex = 4;
            btnExportToExcel.Text = "📊 تصدير Excel";

            btnPrint.Appearance.BackColor = Color.FromArgb(108, 117, 125);
            btnPrint.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnPrint.Appearance.ForeColor = Color.White;
            btnPrint.Appearance.Options.UseBackColor = true;
            btnPrint.Appearance.Options.UseFont = true;
            btnPrint.Appearance.Options.UseForeColor = true;
            btnPrint.Location = new Point(234, 12);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(90, 30);
            btnPrint.TabIndex = 3;
            btnPrint.Text = "🖨️ طباعة";

            btnView.Appearance.BackColor = Color.FromArgb(0, 123, 255);
            btnView.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnView.Appearance.ForeColor = Color.White;
            btnView.Appearance.Options.UseBackColor = true;
            btnView.Appearance.Options.UseFont = true;
            btnView.Appearance.Options.UseForeColor = true;
            btnView.Location = new Point(330, 12);
            btnView.Name = "btnView";
            btnView.Size = new Size(120, 30);
            btnView.TabIndex = 2;
            btnView.Text = "👁️ عرض/تعديل";

            btnNew.Appearance.BackColor = Color.FromArgb(40, 167, 69);
            btnNew.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            btnNew.Appearance.ForeColor = Color.White;
            btnNew.Appearance.Options.UseBackColor = true;
            btnNew.Appearance.Options.UseFont = true;
            btnNew.Appearance.Options.UseForeColor = true;
            btnNew.Location = new Point(456, 12);
            btnNew.Name = "btnNew";
            btnNew.Size = new Size(90, 30);
            btnNew.TabIndex = 1;
            btnNew.Text = "🆕 جديد";

            labelControl1.Appearance.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            labelControl1.Appearance.ForeColor = Color.FromArgb(52, 58, 64);
            labelControl1.Appearance.Options.UseFont = true;
            labelControl1.Appearance.Options.UseForeColor = true;
            labelControl1.Location = new Point(1050, 18);
            labelControl1.Name = "labelControl1";
            labelControl1.Size = new Size(134, 23);
            labelControl1.TabIndex = 0;
            labelControl1.Text = "🚛 إدارة آليات النقل";

            panelControl3.Controls.Add(txtSearch);
            panelControl3.Controls.Add(lblSearch);
            panelControl3.Dock = DockStyle.Bottom;
            panelControl3.Location = new Point(2, 56);
            panelControl3.Name = "panelControl3";
            panelControl3.Size = new Size(1196, 50);
            panelControl3.TabIndex = 1;

            txtSearch.Location = new Point(12, 15);
            txtSearch.Name = "txtSearch";
            txtSearch.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtSearch.Properties.Appearance.Options.UseFont = true;
            txtSearch.Properties.NullText = "ابحث في آليات النقل...";
            txtSearch.Size = new Size(400, 22);
            txtSearch.TabIndex = 1;

            lblSearch.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblSearch.Appearance.ForeColor = Color.FromArgb(52, 58, 64);
            lblSearch.Appearance.Options.UseFont = true;
            lblSearch.Appearance.Options.UseForeColor = true;
            lblSearch.Location = new Point(418, 18);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(35, 16);
            lblSearch.TabIndex = 0;
            lblSearch.Text = "🔍 بحث";

            AutoScaleDimensions = new SizeF(7F, 16F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 600);
            Controls.Add(gridControl1);
            Controls.Add(panelControl1);
            Name = "VehiclesForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "🚛 إدارة آليات النقل";
            ((System.ComponentModel.ISupportInitialize)gridControl1).EndInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).EndInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).EndInit();
            panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)panelControl2).EndInit();
            panelControl2.ResumeLayout(false);
            panelControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl3).EndInit();
            panelControl3.ResumeLayout(false);
            panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).EndInit();
            ResumeLayout(false);
        }

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleId;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleName;
        private DevExpress.XtraGrid.Columns.GridColumn colTransportCompanyName;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleTypeName;
        private DevExpress.XtraGrid.Columns.GridColumn colDescription;
        private DevExpress.XtraGrid.Columns.GridColumn colCreatedAt;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraEditors.SimpleButton btnExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraEditors.SimpleButton btnView;
        private DevExpress.XtraEditors.SimpleButton btnNew;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraEditors.TextEdit txtSearch;
        private DevExpress.XtraEditors.LabelControl lblSearch;
    }
}
