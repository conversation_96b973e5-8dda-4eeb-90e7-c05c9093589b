namespace WinFormsApp1.Forms
{
    partial class CustomsDataForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            gridControl1 = new DevExpress.XtraGrid.GridControl();
            gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            colCustomsDataId = new DevExpress.XtraGrid.Columns.GridColumn();
            colCustomsNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            colCustomsDate = new DevExpress.XtraGrid.Columns.GridColumn();
            colCustomsType = new DevExpress.XtraGrid.Columns.GridColumn();
            colMaterialName = new DevExpress.XtraGrid.Columns.GridColumn();
            colImporterName = new DevExpress.XtraGrid.Columns.GridColumn();
            colVesselName = new DevExpress.XtraGrid.Columns.GridColumn();
            colCommonName = new DevExpress.XtraGrid.Columns.GridColumn();
            colQuantity = new DevExpress.XtraGrid.Columns.GridColumn();
            colCreatedAt = new DevExpress.XtraGrid.Columns.GridColumn();
            panelControl1 = new DevExpress.XtraEditors.PanelControl();
            panelControl2 = new DevExpress.XtraEditors.PanelControl();
            btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            btnExportToExcel = new DevExpress.XtraEditors.SimpleButton();
            btnPrint = new DevExpress.XtraEditors.SimpleButton();
            btnView = new DevExpress.XtraEditors.SimpleButton();
            btnNew = new DevExpress.XtraEditors.SimpleButton();
            labelControl1 = new DevExpress.XtraEditors.LabelControl();
            panelControl3 = new DevExpress.XtraEditors.PanelControl();
            txtSearch = new DevExpress.XtraEditors.TextEdit();
            labelControl2 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)gridControl1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).BeginInit();
            panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl2).BeginInit();
            panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl3).BeginInit();
            panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).BeginInit();
            SuspendLayout();
            // 
            // gridControl1
            // 
            gridControl1.Dock = DockStyle.Fill;
            gridControl1.EmbeddedNavigator.Margin = new Padding(3, 2, 3, 2);
            gridControl1.Location = new Point(0, 108);
            gridControl1.MainView = gridView1;
            gridControl1.Margin = new Padding(3, 2, 3, 2);
            gridControl1.Name = "gridControl1";
            gridControl1.Size = new Size(686, 258);
            gridControl1.TabIndex = 0;
            gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] { gridView1 });
            // 
            // gridView1
            // 
            gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] { colCustomsDataId, colCustomsNumber, colCustomsDate, colCustomsType, colMaterialName, colImporterName, colVesselName, colCommonName, colQuantity, colCreatedAt });
            gridView1.GridControl = gridControl1;
            gridView1.Name = "gridView1";
            gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // colCustomsDataId
            // 
            colCustomsDataId.Caption = "رقم البيان";
            colCustomsDataId.FieldName = "CustomsDataId";
            colCustomsDataId.Name = "colCustomsDataId";
            colCustomsDataId.Visible = true;
            colCustomsDataId.VisibleIndex = 0;
            colCustomsDataId.Width = 80;
            // 
            // colCustomsNumber
            // 
            colCustomsNumber.Caption = "رقم الجمرك";
            colCustomsNumber.FieldName = "CustomsNumber";
            colCustomsNumber.Name = "colCustomsNumber";
            colCustomsNumber.Visible = true;
            colCustomsNumber.VisibleIndex = 1;
            colCustomsNumber.Width = 120;
            // 
            // colCustomsDate
            // 
            colCustomsDate.Caption = "تاريخ الجمرك";
            colCustomsDate.FieldName = "CustomsDate";
            colCustomsDate.Name = "colCustomsDate";
            colCustomsDate.Visible = true;
            colCustomsDate.VisibleIndex = 2;
            colCustomsDate.Width = 120;
            // 
            // colCustomsType
            // 
            colCustomsType.Caption = "نوع الجمرك";
            colCustomsType.FieldName = "CustomsType";
            colCustomsType.Name = "colCustomsType";
            colCustomsType.Visible = true;
            colCustomsType.VisibleIndex = 3;
            colCustomsType.Width = 100;
            // 
            // colMaterialName
            // 
            colMaterialName.Caption = "المادة";
            colMaterialName.FieldName = "Material.MaterialName";
            colMaterialName.Name = "colMaterialName";
            colMaterialName.Visible = true;
            colMaterialName.VisibleIndex = 4;
            colMaterialName.Width = 150;
            // 
            // colImporterName
            // 
            colImporterName.Caption = "المستورد";
            colImporterName.FieldName = "Importer.Name";
            colImporterName.Name = "colImporterName";
            colImporterName.Visible = true;
            colImporterName.VisibleIndex = 5;
            colImporterName.Width = 150;
            // 
            // colVesselName
            // 
            colVesselName.Caption = "الباخرة";
            colVesselName.FieldName = "Vessel.VesselName";
            colVesselName.Name = "colVesselName";
            colVesselName.Visible = true;
            colVesselName.VisibleIndex = 6;
            colVesselName.Width = 120;
            // 
            // colCommonName
            // 
            colCommonName.Caption = "الاسم الشائع";
            colCommonName.FieldName = "CommonName";
            colCommonName.Name = "colCommonName";
            colCommonName.Visible = true;
            colCommonName.VisibleIndex = 7;
            colCommonName.Width = 120;
            // 
            // colQuantity
            // 
            colQuantity.Caption = "الكمية";
            colQuantity.FieldName = "Quantity";
            colQuantity.Name = "colQuantity";
            colQuantity.Visible = true;
            colQuantity.VisibleIndex = 8;
            colQuantity.Width = 80;
            // 
            // colCreatedAt
            // 
            colCreatedAt.Caption = "تاريخ الإنشاء";
            colCreatedAt.FieldName = "CreatedAt";
            colCreatedAt.Name = "colCreatedAt";
            colCreatedAt.Visible = true;
            colCreatedAt.VisibleIndex = 9;
            colCreatedAt.Width = 130;
            // 
            // panelControl1
            // 
            panelControl1.Appearance.BackColor = Color.FromArgb(248, 249, 250);
            panelControl1.Appearance.Options.UseBackColor = true;
            panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl1.Controls.Add(panelControl2);
            panelControl1.Controls.Add(labelControl1);
            panelControl1.Dock = DockStyle.Top;
            panelControl1.Location = new Point(0, 0);
            panelControl1.Margin = new Padding(3, 2, 3, 2);
            panelControl1.Name = "panelControl1";
            panelControl1.Size = new Size(686, 68);
            panelControl1.TabIndex = 1;
            // 
            // panelControl2
            // 
            panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl2.Controls.Add(btnRefresh);
            panelControl2.Controls.Add(btnExportToExcel);
            panelControl2.Controls.Add(btnPrint);
            panelControl2.Controls.Add(btnView);
            panelControl2.Controls.Add(btnNew);
            panelControl2.Dock = DockStyle.Left;
            panelControl2.Location = new Point(0, 39);
            panelControl2.Margin = new Padding(3, 2, 3, 2);
            panelControl2.Name = "panelControl2";
            panelControl2.Size = new Size(425, 29);
            panelControl2.TabIndex = 1;
            // 
            // btnRefresh
            // 
            btnRefresh.Appearance.BackColor = Color.FromArgb(52, 152, 219);
            btnRefresh.Appearance.BorderColor = Color.FromArgb(41, 128, 185);
            btnRefresh.Appearance.ForeColor = Color.White;
            btnRefresh.Appearance.Options.UseBackColor = true;
            btnRefresh.Appearance.Options.UseBorderColor = true;
            btnRefresh.Appearance.Options.UseForeColor = true;
            btnRefresh.Dock = DockStyle.Left;
            btnRefresh.ImageOptions.SvgImageSize = new Size(16, 16);
            btnRefresh.Location = new Point(320, 0);
            btnRefresh.Margin = new Padding(3, 2, 3, 2);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(80, 29);
            btnRefresh.TabIndex = 4;
            btnRefresh.Text = "🔄 تحديث";
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnExportToExcel
            // 
            btnExportToExcel.Appearance.BackColor = Color.FromArgb(39, 174, 96);
            btnExportToExcel.Appearance.BorderColor = Color.FromArgb(34, 153, 84);
            btnExportToExcel.Appearance.ForeColor = Color.White;
            btnExportToExcel.Appearance.Options.UseBackColor = true;
            btnExportToExcel.Appearance.Options.UseBorderColor = true;
            btnExportToExcel.Appearance.Options.UseForeColor = true;
            btnExportToExcel.Dock = DockStyle.Left;
            btnExportToExcel.ImageOptions.SvgImageSize = new Size(16, 16);
            btnExportToExcel.Location = new Point(240, 0);
            btnExportToExcel.Margin = new Padding(3, 2, 3, 2);
            btnExportToExcel.Name = "btnExportToExcel";
            btnExportToExcel.Size = new Size(80, 29);
            btnExportToExcel.TabIndex = 3;
            btnExportToExcel.Text = "📊 تصدير Excel";
            btnExportToExcel.Click += btnExportToExcel_Click;
            // 
            // btnPrint
            // 
            btnPrint.Appearance.BackColor = Color.FromArgb(155, 89, 182);
            btnPrint.Appearance.BorderColor = Color.FromArgb(142, 68, 173);
            btnPrint.Appearance.ForeColor = Color.White;
            btnPrint.Appearance.Options.UseBackColor = true;
            btnPrint.Appearance.Options.UseBorderColor = true;
            btnPrint.Appearance.Options.UseForeColor = true;
            btnPrint.Dock = DockStyle.Left;
            btnPrint.ImageOptions.SvgImageSize = new Size(16, 16);
            btnPrint.Location = new Point(160, 0);
            btnPrint.Margin = new Padding(3, 2, 3, 2);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(80, 29);
            btnPrint.TabIndex = 2;
            btnPrint.Text = "🖨️ طباعة";
            btnPrint.Click += btnPrint_Click;
            // 
            // btnView
            // 
            btnView.Appearance.BackColor = Color.FromArgb(241, 196, 15);
            btnView.Appearance.BorderColor = Color.FromArgb(243, 156, 18);
            btnView.Appearance.ForeColor = Color.White;
            btnView.Appearance.Options.UseBackColor = true;
            btnView.Appearance.Options.UseBorderColor = true;
            btnView.Appearance.Options.UseForeColor = true;
            btnView.Dock = DockStyle.Left;
            btnView.ImageOptions.SvgImageSize = new Size(16, 16);
            btnView.Location = new Point(80, 0);
            btnView.Margin = new Padding(3, 2, 3, 2);
            btnView.Name = "btnView";
            btnView.Size = new Size(80, 29);
            btnView.TabIndex = 1;
            btnView.Text = "👁️ عرض";
            btnView.Click += btnView_Click;
            // 
            // btnNew
            // 
            btnNew.Appearance.BackColor = Color.FromArgb(46, 204, 113);
            btnNew.Appearance.BorderColor = Color.FromArgb(39, 174, 96);
            btnNew.Appearance.ForeColor = Color.White;
            btnNew.Appearance.Options.UseBackColor = true;
            btnNew.Appearance.Options.UseBorderColor = true;
            btnNew.Appearance.Options.UseForeColor = true;
            btnNew.Dock = DockStyle.Left;
            btnNew.ImageOptions.SvgImageSize = new Size(16, 16);
            btnNew.Location = new Point(0, 0);
            btnNew.Margin = new Padding(3, 2, 3, 2);
            btnNew.Name = "btnNew";
            btnNew.Size = new Size(80, 29);
            btnNew.TabIndex = 0;
            btnNew.Text = "🆕 جديد";
            btnNew.Click += btnNew_Click;
            // 
            // labelControl1
            // 
            labelControl1.Appearance.Font = new Font("Tahoma", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            labelControl1.Appearance.ForeColor = Color.FromArgb(44, 62, 80);
            labelControl1.Appearance.Options.UseFont = true;
            labelControl1.Appearance.Options.UseForeColor = true;
            labelControl1.Appearance.Options.UseTextOptions = true;
            labelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            labelControl1.Dock = DockStyle.Top;
            labelControl1.Location = new Point(0, 0);
            labelControl1.Margin = new Padding(3, 2, 3, 2);
            labelControl1.Name = "labelControl1";
            labelControl1.Padding = new Padding(0, 12, 0, 8);
            labelControl1.Size = new Size(334, 39);
            labelControl1.TabIndex = 0;
            labelControl1.Text = "📋 إدارة البيانات الجمركية - إجمالي البيانات: 0";
            // 
            // panelControl3
            // 
            panelControl3.Appearance.BackColor = Color.FromArgb(236, 240, 241);
            panelControl3.Appearance.Options.UseBackColor = true;
            panelControl3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl3.Controls.Add(txtSearch);
            panelControl3.Controls.Add(labelControl2);
            panelControl3.Dock = DockStyle.Top;
            panelControl3.Location = new Point(0, 68);
            panelControl3.Margin = new Padding(3, 2, 3, 2);
            panelControl3.Name = "panelControl3";
            panelControl3.Size = new Size(686, 40);
            panelControl3.TabIndex = 2;
            // 
            // txtSearch
            // 
            txtSearch.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            txtSearch.Location = new Point(12, 8);
            txtSearch.Margin = new Padding(3, 2, 3, 2);
            txtSearch.Name = "txtSearch";
            txtSearch.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtSearch.Properties.Appearance.Options.UseFont = true;
            txtSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            txtSearch.Properties.NullText = "🔍 البحث في البيانات الجمركية (رقم الجمرك، نوع الجمرك، المادة، المستورد، الباخرة، الاسم الشائع، الرقم)...";
            txtSearch.Properties.NullValuePrompt = "🔍 البحث في البيانات الجمركية...";
            txtSearch.Size = new Size(580, 24);
            txtSearch.TabIndex = 1;
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;
            // 
            // labelControl2
            // 
            labelControl2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            labelControl2.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            labelControl2.Appearance.ForeColor = Color.FromArgb(52, 73, 94);
            labelControl2.Appearance.Options.UseFont = true;
            labelControl2.Appearance.Options.UseForeColor = true;
            labelControl2.Location = new Point(598, 10);
            labelControl2.Margin = new Padding(3, 2, 3, 2);
            labelControl2.Name = "labelControl2";
            labelControl2.Size = new Size(65, 17);
            labelControl2.TabIndex = 0;
            labelControl2.Text = "🔍 البحث:";
            // 
            // CustomsDataForm
            // 
            AutoScaleDimensions = new SizeF(6F, 13F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(686, 366);
            Controls.Add(gridControl1);
            Controls.Add(panelControl3);
            Controls.Add(panelControl1);
            Margin = new Padding(3, 2, 3, 2);
            Name = "CustomsDataForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "إدارة البيانات الجمركية";
            WindowState = FormWindowState.Maximized;
            ((System.ComponentModel.ISupportInitialize)gridControl1).EndInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).EndInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).EndInit();
            panelControl1.ResumeLayout(false);
            panelControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl2).EndInit();
            panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)panelControl3).EndInit();
            panelControl3.ResumeLayout(false);
            panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colCustomsDataId;
        private DevExpress.XtraGrid.Columns.GridColumn colCustomsNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colCustomsDate;
        private DevExpress.XtraGrid.Columns.GridColumn colCustomsType;
        private DevExpress.XtraGrid.Columns.GridColumn colMaterialName;
        private DevExpress.XtraGrid.Columns.GridColumn colImporterName;
        private DevExpress.XtraGrid.Columns.GridColumn colVesselName;
        private DevExpress.XtraGrid.Columns.GridColumn colCommonName;
        private DevExpress.XtraGrid.Columns.GridColumn colQuantity;
        private DevExpress.XtraGrid.Columns.GridColumn colCreatedAt;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraEditors.SimpleButton btnExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraEditors.SimpleButton btnView;
        private DevExpress.XtraEditors.SimpleButton btnNew;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraEditors.TextEdit txtSearch;
        private DevExpress.XtraEditors.LabelControl labelControl2;
    }
}
