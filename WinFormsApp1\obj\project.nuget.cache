{"version": 2, "dgSpecHash": "J75LsFnE25c=", "success": true, "projectFilePath": "D:\\tranV1.1\\WinFormsApp1\\WinFormsApp1.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.charts\\24.2.7\\devexpress.charts.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.charts.core\\24.2.7\\devexpress.charts.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.codeparser\\24.2.7\\devexpress.codeparser.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.data\\24.2.7\\devexpress.data.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.data.desktop\\24.2.7\\devexpress.data.desktop.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.dataaccess\\24.2.7\\devexpress.dataaccess.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.dataaccess.ui\\24.2.7\\devexpress.dataaccess.ui.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.datavisualization.core\\24.2.7\\devexpress.datavisualization.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.diagram.core\\24.2.7\\devexpress.diagram.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.drawing\\24.2.7\\devexpress.drawing.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.gauges.core\\24.2.7\\devexpress.gauges.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.images\\24.2.7\\devexpress.images.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.map.core\\24.2.7\\devexpress.map.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.mvvm\\24.2.7\\devexpress.mvvm.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.office.core\\24.2.7\\devexpress.office.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.core\\24.2.7\\devexpress.pdf.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pdf.drawing\\24.2.7\\devexpress.pdf.drawing.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.pivotgrid.core\\24.2.7\\devexpress.pivotgrid.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.printing.core\\24.2.7\\devexpress.printing.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.reporting.core\\24.2.7\\devexpress.reporting.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.richedit.core\\24.2.7\\devexpress.richedit.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.richedit.export\\24.2.7\\devexpress.richedit.export.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.scheduler.core\\24.2.7\\devexpress.scheduler.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.scheduler.coredesktop\\24.2.7\\devexpress.scheduler.coredesktop.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.sparkline.core\\24.2.7\\devexpress.sparkline.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.spellchecker.core\\24.2.7\\devexpress.spellchecker.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.spreadsheet.core\\24.2.7\\devexpress.spreadsheet.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.treemap\\24.2.7\\devexpress.treemap.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.treemap.core\\24.2.7\\devexpress.treemap.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.utils\\24.2.7\\devexpress.utils.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.utils.ui\\24.2.7\\devexpress.utils.ui.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win\\24.2.7\\devexpress.win.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.charts\\24.2.7\\devexpress.win.charts.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.design\\24.2.7\\devexpress.win.design.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.diagram\\24.2.7\\devexpress.win.diagram.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.dialogs\\24.2.7\\devexpress.win.dialogs.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.dialogs.core\\24.2.7\\devexpress.win.dialogs.core.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.gantt\\24.2.7\\devexpress.win.gantt.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.gauges\\24.2.7\\devexpress.win.gauges.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.grid\\24.2.7\\devexpress.win.grid.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.map\\24.2.7\\devexpress.win.map.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.navigation\\24.2.7\\devexpress.win.navigation.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.pdfviewer\\24.2.7\\devexpress.win.pdfviewer.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.pivotgrid\\24.2.7\\devexpress.win.pivotgrid.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.printing\\24.2.7\\devexpress.win.printing.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.reporting\\24.2.7\\devexpress.win.reporting.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.richedit\\24.2.7\\devexpress.win.richedit.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.scheduler\\24.2.7\\devexpress.win.scheduler.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.schedulerextensions\\24.2.7\\devexpress.win.schedulerextensions.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.schedulerreporting\\24.2.7\\devexpress.win.schedulerreporting.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.spellchecker\\24.2.7\\devexpress.win.spellchecker.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.spreadsheet\\24.2.7\\devexpress.win.spreadsheet.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.treelist\\24.2.7\\devexpress.win.treelist.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.treemap\\24.2.7\\devexpress.win.treemap.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.win.verticalgrid\\24.2.7\\devexpress.win.verticalgrid.24.2.7.nupkg.sha512", "C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages\\devexpress.xpo\\24.2.7\\devexpress.xpo.24.2.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\humanizer.core.2.14.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\7.0.0\\microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\17.8.3\\microsoft.build.framework.17.8.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.locator\\1.7.8\\microsoft.build.locator.1.7.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.analyzers\\3.3.4\\microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.8.0\\microsoft.codeanalysis.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.8.0\\microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.8.0\\microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.8.0\\microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.msbuild\\4.8.0\\microsoft.codeanalysis.workspaces.msbuild.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\5.1.6\\microsoft.data.sqlclient.5.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\5.1.1\\microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\9.0.6\\microsoft.entityframeworkcore.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\9.0.6\\microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.analyzers\\9.0.6\\microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\9.0.6\\microsoft.entityframeworkcore.design.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\9.0.6\\microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlserver\\9.0.6\\microsoft.entityframeworkcore.sqlserver.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.tools\\9.0.6\\microsoft.entityframeworkcore.tools.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.6\\microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\9.0.6\\microsoft.extensions.caching.memory.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.6\\microsoft.extensions.configuration.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.6\\microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.6\\microsoft.extensions.configuration.fileextensions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.6\\microsoft.extensions.configuration.json.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.6\\microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.6\\microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.6\\microsoft.extensions.dependencymodel.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.6\\microsoft.extensions.fileproviders.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.6\\microsoft.extensions.fileproviders.physical.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.6\\microsoft.extensions.filesystemglobbing.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.6\\microsoft.extensions.logging.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.6\\microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.objectpool\\6.0.16\\microsoft.extensions.objectpool.6.0.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.6\\microsoft.extensions.options.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.6\\microsoft.extensions.primitives.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\6.35.0\\microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\6.35.0\\microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\3.1.4\\microsoft.netcore.platforms.3.1.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\4.7.0\\microsoft.win32.registry.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\4.7.0\\microsoft.win32.systemevents.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.texttemplating\\3.0.0\\mono.texttemplating.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.native.system.data.sqlclient.sni\\4.7.0\\runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x64.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\runtime.win-x86.runtime.native.system.data.sqlclient.sni\\4.4.0\\runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\7.0.0\\system.collections.immutable.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition\\7.0.0\\system.composition.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\7.0.0\\system.composition.attributedmodel.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\7.0.0\\system.composition.convention.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\7.0.0\\system.composition.hosting.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\7.0.0\\system.composition.runtime.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\7.0.0\\system.composition.typedparts.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.1\\system.configuration.configurationmanager.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.oledb\\8.0.1\\system.data.oledb.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.data.sqlclient\\4.8.6\\system.data.sqlclient.4.8.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\8.0.1\\system.diagnostics.eventlog.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.performancecounter\\8.0.1\\system.diagnostics.performancecounter.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\4.7.2\\system.drawing.common.4.7.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\9.0.6\\system.formats.asn1.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\7.0.0\\system.io.pipelines.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.metadata\\7.0.0\\system.reflection.metadata.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\8.0.0\\system.security.cryptography.protecteddata.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\6.0.1\\system.security.cryptography.xml.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.http\\6.2.0\\system.servicemodel.http.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.netframingbase\\6.2.0\\system.servicemodel.netframingbase.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.nettcp\\6.2.0\\system.servicemodel.nettcp.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.servicemodel.primitives\\6.2.0\\system.servicemodel.primitives.6.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.6\\system.text.json.9.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\7.0.0\\system.threading.channels.7.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512"], "logs": []}