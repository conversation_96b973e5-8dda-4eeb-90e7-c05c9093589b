﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Compile Update="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CurrenciesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CurrencyAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CurrencyEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\CustomsDataForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ImporterAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\ImportersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MasterF.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MaterialAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MaterialsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MerchantAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\MerchantsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PartiesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\PartyAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TestTransportCompanyForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TransportCompaniesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\TransportCompanyAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\UserAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\UsersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\VehicleAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\VehiclesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\VesselAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\VesselsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\WarehouseAddEditForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="Forms\WarehousesForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>