﻿namespace WinFormsApp1.Forms
{
    partial class CustomsDataAddEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(CustomsDataAddEditForm));
            barManager2 = new DevExpress.XtraBars.BarManager(components);
            bar6 = new DevExpress.XtraBars.Bar();
            barButtonItem_New = new DevExpress.XtraBars.BarButtonItem();
            barButtonItem_Save = new DevExpress.XtraBars.BarButtonItem();
            barButtonItem_Delete = new DevExpress.XtraBars.BarButtonItem();
            barButtonItem_Print = new DevExpress.XtraBars.BarButtonItem();
            barDockControl1 = new DevExpress.XtraBars.BarDockControl();
            barDockControl2 = new DevExpress.XtraBars.BarDockControl();
            barDockControl3 = new DevExpress.XtraBars.BarDockControl();
            barDockControl4 = new DevExpress.XtraBars.BarDockControl();
            barManager1 = new DevExpress.XtraBars.BarManager(components);
            bar3 = new DevExpress.XtraBars.Bar();
            barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            barEditItem1 = new DevExpress.XtraBars.BarEditItem();
            repositoryItemTextEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            ((System.ComponentModel.ISupportInitialize)barManager2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)barManager1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)repositoryItemTextEdit2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)repositoryItemTextEdit1).BeginInit();
            SuspendLayout();
            // 
            // barManager2
            // 
            barManager2.Bars.AddRange(new DevExpress.XtraBars.Bar[] { bar6 });
            barManager2.DockControls.Add(barDockControl1);
            barManager2.DockControls.Add(barDockControl2);
            barManager2.DockControls.Add(barDockControl3);
            barManager2.DockControls.Add(barDockControl4);
            barManager2.Form = this;
            barManager2.Items.AddRange(new DevExpress.XtraBars.BarItem[] { barButtonItem_New, barButtonItem_Save, barButtonItem_Delete, barButtonItem_Print });
            barManager2.MaxItemId = 4;
            barManager2.RightToLeft = DevExpress.Utils.DefaultBoolean.True;
            barManager2.StatusBar = bar6;
            // 
            // bar6
            // 
            bar6.BarName = "Status bar";
            bar6.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            bar6.DockCol = 0;
            bar6.DockRow = 0;
            bar6.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            bar6.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] { new DevExpress.XtraBars.LinkPersistInfo(barButtonItem_New), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem_Save), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem_Delete), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem_Print) });
            bar6.OptionsBar.AllowQuickCustomization = false;
            bar6.OptionsBar.DrawDragBorder = false;
            bar6.OptionsBar.UseWholeRow = true;
            bar6.Text = "Status bar";
            // 
            // barButtonItem_New
            // 
            barButtonItem_New.Caption = "جديد";
            barButtonItem_New.Id = 0;
            barButtonItem_New.Name = "barButtonItem_New";
            // 
            // barButtonItem_Save
            // 
            barButtonItem_Save.Caption = "💾 حفظ";
            barButtonItem_Save.Id = 1;
            barButtonItem_Save.Name = "barButtonItem_Save";
            // 
            // barButtonItem_Delete
            // 
            barButtonItem_Delete.Caption = "حذف";
            barButtonItem_Delete.Id = 2;
            barButtonItem_Delete.Name = "barButtonItem_Delete";
            // 
            // barButtonItem_Print
            // 
            barButtonItem_Print.Caption = "طباعة";
            barButtonItem_Print.Id = 3;
            barButtonItem_Print.Name = "barButtonItem_Print";
            // 
            // barDockControl1
            // 
            barDockControl1.CausesValidation = false;
            barDockControl1.Dock = DockStyle.Top;
            barDockControl1.Location = new Point(0, 0);
            barDockControl1.Manager = barManager2;
            barDockControl1.Size = new Size(398, 0);
            // 
            // barDockControl2
            // 
            barDockControl2.CausesValidation = false;
            barDockControl2.Dock = DockStyle.Bottom;
            barDockControl2.Location = new Point(0, 279);
            barDockControl2.Manager = barManager2;
            barDockControl2.Size = new Size(398, 22);
            // 
            // barDockControl3
            // 
            barDockControl3.CausesValidation = false;
            barDockControl3.Dock = DockStyle.Left;
            barDockControl3.Location = new Point(0, 0);
            barDockControl3.Manager = barManager2;
            barDockControl3.Size = new Size(0, 279);
            // 
            // barDockControl4
            // 
            barDockControl4.CausesValidation = false;
            barDockControl4.Dock = DockStyle.Right;
            barDockControl4.Location = new Point(398, 0);
            barDockControl4.Manager = barManager2;
            barDockControl4.Size = new Size(0, 279);
            // 
            // barManager1
            // 
            barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] { bar3 });
            barManager1.DockControls.Add(barDockControlTop);
            barManager1.DockControls.Add(barDockControlBottom);
            barManager1.DockControls.Add(barDockControlLeft);
            barManager1.DockControls.Add(barDockControlRight);
            barManager1.Form = this;
            barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] { barButtonItem1, barButtonItem2, barEditItem1, barButtonItem3, barButtonItem4 });
            barManager1.MaxItemId = 7;
            barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] { repositoryItemTextEdit1, repositoryItemTextEdit2 });
            barManager1.RightToLeft = DevExpress.Utils.DefaultBoolean.False;
            barManager1.StatusBar = bar3;
            // 
            // bar3
            // 
            bar3.BarName = "Status bar";
            bar3.CanDockStyle = DevExpress.XtraBars.BarCanDockStyle.Bottom;
            bar3.DockCol = 0;
            bar3.DockRow = 0;
            bar3.DockStyle = DevExpress.XtraBars.BarDockStyle.Bottom;
            bar3.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] { new DevExpress.XtraBars.LinkPersistInfo(barButtonItem1), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem2), new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.Width, barEditItem1, "", false, true, true, 66), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem3), new DevExpress.XtraBars.LinkPersistInfo(barButtonItem4) });
            bar3.OptionsBar.AllowQuickCustomization = false;
            bar3.OptionsBar.DrawDragBorder = false;
            bar3.OptionsBar.UseWholeRow = true;
            bar3.Text = "Status bar";
            // 
            // barButtonItem1
            // 
            barButtonItem1.Caption = "Last";
            barButtonItem1.Id = 0;
            barButtonItem1.ImageOptions.SvgImage = (DevExpress.Utils.Svg.SvgImage)resources.GetObject("barButtonItem1.ImageOptions.SvgImage");
            barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            barButtonItem2.Caption = "Previous";
            barButtonItem2.Id = 1;
            barButtonItem2.ImageOptions.SvgImage = (DevExpress.Utils.Svg.SvgImage)resources.GetObject("barButtonItem2.ImageOptions.SvgImage");
            barButtonItem2.Name = "barButtonItem2";
            // 
            // barEditItem1
            // 
            barEditItem1.Caption = "barEditItem1";
            barEditItem1.Edit = repositoryItemTextEdit2;
            barEditItem1.Id = 3;
            barEditItem1.Name = "barEditItem1";
            // 
            // repositoryItemTextEdit2
            // 
            repositoryItemTextEdit2.AutoHeight = false;
            repositoryItemTextEdit2.Name = "repositoryItemTextEdit2";
            // 
            // barButtonItem3
            // 
            barButtonItem3.Caption = "Next";
            barButtonItem3.Id = 4;
            barButtonItem3.ImageOptions.SvgImage = (DevExpress.Utils.Svg.SvgImage)resources.GetObject("barButtonItem3.ImageOptions.SvgImage");
            barButtonItem3.Name = "barButtonItem3";
            // 
            // barButtonItem4
            // 
            barButtonItem4.Caption = "First";
            barButtonItem4.Id = 5;
            barButtonItem4.ImageOptions.SvgImage = (DevExpress.Utils.Svg.SvgImage)resources.GetObject("barButtonItem4.ImageOptions.SvgImage");
            barButtonItem4.Name = "barButtonItem4";
            // 
            // barDockControlTop
            // 
            barDockControlTop.CausesValidation = false;
            barDockControlTop.Dock = DockStyle.Top;
            barDockControlTop.Location = new Point(0, 0);
            barDockControlTop.Manager = barManager1;
            barDockControlTop.Size = new Size(398, 0);
            // 
            // barDockControlBottom
            // 
            barDockControlBottom.CausesValidation = false;
            barDockControlBottom.Dock = DockStyle.Bottom;
            barDockControlBottom.Location = new Point(0, 301);
            barDockControlBottom.Manager = barManager1;
            barDockControlBottom.Size = new Size(398, 26);
            // 
            // barDockControlLeft
            // 
            barDockControlLeft.CausesValidation = false;
            barDockControlLeft.Dock = DockStyle.Left;
            barDockControlLeft.Location = new Point(0, 0);
            barDockControlLeft.Manager = barManager1;
            barDockControlLeft.Size = new Size(0, 301);
            // 
            // barDockControlRight
            // 
            barDockControlRight.CausesValidation = false;
            barDockControlRight.Dock = DockStyle.Right;
            barDockControlRight.Location = new Point(398, 0);
            barDockControlRight.Manager = barManager1;
            barDockControlRight.Size = new Size(0, 301);
            // 
            // repositoryItemTextEdit1
            // 
            repositoryItemTextEdit1.AutoHeight = false;
            repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // CustomsDataAddEditForm
            // 
            AutoScaleDimensions = new SizeF(6F, 13F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(398, 327);
            Controls.Add(barDockControl3);
            Controls.Add(barDockControl4);
            Controls.Add(barDockControl2);
            Controls.Add(barDockControl1);
            Controls.Add(barDockControlLeft);
            Controls.Add(barDockControlRight);
            Controls.Add(barDockControlBottom);
            Controls.Add(barDockControlTop);
            Name = "CustomsDataAddEditForm";
            Text = "CustomsDataAddEditForm";
            ((System.ComponentModel.ISupportInitialize)barManager2).EndInit();
            ((System.ComponentModel.ISupportInitialize)barManager1).EndInit();
            ((System.ComponentModel.ISupportInitialize)repositoryItemTextEdit2).EndInit();
            ((System.ComponentModel.ISupportInitialize)repositoryItemTextEdit1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        protected DevExpress.XtraBars.BarManager barManager2;
        protected DevExpress.XtraBars.Bar bar6;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem_New;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem_Save;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem_Delete;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem_Print;
        protected DevExpress.XtraBars.BarDockControl barDockControl1;
        protected DevExpress.XtraBars.BarDockControl barDockControl2;
        protected DevExpress.XtraBars.BarDockControl barDockControl3;
        protected DevExpress.XtraBars.BarDockControl barDockControl4;
        protected DevExpress.XtraBars.BarDockControl barDockControlLeft;
        protected DevExpress.XtraBars.BarManager barManager1;
        protected DevExpress.XtraBars.Bar bar3;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem1;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem2;
        protected DevExpress.XtraBars.BarEditItem barEditItem1;
        protected DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit2;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem3;
        protected DevExpress.XtraBars.BarButtonItem barButtonItem4;
        protected DevExpress.XtraBars.BarDockControl barDockControlTop;
        protected DevExpress.XtraBars.BarDockControl barDockControlBottom;
        protected DevExpress.XtraBars.BarDockControl barDockControlRight;
        protected DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
    }
}