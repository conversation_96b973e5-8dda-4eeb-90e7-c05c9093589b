using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Windows.Forms;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// فورم عرض جدول آليات النقل مع عمليات CRUD
    /// </summary>
    public partial class VehiclesForm : XtraForm
    {
        #region Fields

        private readonly WarehouseDbContext _context;
        private bool _isFormOpening = false; // لمنع فتح النافذة أكثر من مرة

        #endregion

        #region Constructor

        public VehiclesForm()
        {
            try
            {
                InitializeComponent();
                _context = DatabaseService.GetDbContext();
                SetupForm();
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج آليات النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                message += $"\n\nStack Trace:\n{ex.StackTrace}";
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Setup Methods

        /// <summary>
        /// إعداد النموذج الأساسي
        /// </summary>
        private void SetupForm()
        {
            this.Text = "🚛 إدارة آليات النقل";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // إعداد نصوص الأزرار
            btnNew.Text = "🆕 جديد";
            btnView.Text = "👁️ عرض";
            btnPrint.Text = "🖨️ طباعة";
            btnExportToExcel.Text = "📊 تصدير Excel";
            btnRefresh.Text = "🔄 تحديث";

            // إعداد التلميحات
            btnNew.ToolTip = "إنشاء آلية نقل جديدة (Ctrl+N)";
            btnView.ToolTip = "عرض/تعديل آلية النقل المحددة (Enter)";
            btnPrint.ToolTip = "طباعة قائمة آليات النقل (Ctrl+P)";
            btnExportToExcel.ToolTip = "تصدير البيانات إلى Excel (Ctrl+E)";
            btnRefresh.ToolTip = "تحديث البيانات (F5)";

            // إعداد الجدول
            SetupGrid();

            // إعداد اختصارات لوحة المفاتيح
            this.KeyPreview = true;
            this.KeyDown += VehiclesForm_KeyDown;

            // إعداد حدث تغيير حجم النافذة
            this.Resize += VehiclesForm_Resize;
        }

        /// <summary>
        /// إعداد الجدول
        /// </summary>
        private void SetupGrid()
        {
            // إعداد العرض العام
            GridView1.OptionsView.ShowGroupPanel = false;
            GridView1.OptionsView.ColumnAutoWidth = true; // تفعيل العرض التلقائي
            GridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            GridView1.OptionsSelection.EnableAppearanceFocusedRow = true;

            // إعدادات إضافية لضمان امتداد الأعمدة
            GridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full;
            GridView1.OptionsView.BestFitMaxRowCount = 100;
            GridView1.HorzScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Never;

            // إعداد أحداث الجدول
            GridView1.DoubleClick += GridView1_DoubleClick;
            GridView1.KeyDown += GridView1_KeyDown;
            GridView1.DataSourceChanged += GridView1_DataSourceChanged;

            // إعداد أحداث الأزرار
            btnNew.Click += btnNew_Click;
            btnView.Click += btnView_Click;
            btnRefresh.Click += btnRefresh_Click;
            btnPrint.Click += btnPrint_Click;
            btnExportToExcel.Click += btnExportToExcel_Click;

            // إعداد حدث البحث
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;
        }

        #endregion

        #region Form Events

        /// <summary>
        /// معالج حدث تغيير حجم النافذة
        /// </summary>
        private void VehiclesForm_Resize(object sender, EventArgs e)
        {
            // إعادة تعديل عرض الأعمدة عند تغيير حجم النافذة
            if (GridView1 != null && this.WindowState != FormWindowState.Minimized && GridControl1.Width > 0)
            {
                // تأخير قصير لضمان اكتمال تغيير الحجم
                System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
                timer.Interval = 100;
                timer.Tick += (s, args) =>
                {
                    timer.Stop();
                    timer.Dispose();
                    OptimizeColumnWidths();
                };
                timer.Start();
            }
        }

        #endregion

        #region Keyboard Events

        /// <summary>
        /// أحداث لوحة المفاتيح للنموذج
        /// </summary>
        private void VehiclesForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control)
            {
                switch (e.KeyCode)
                {
                    case Keys.N:
                        if (!_isFormOpening)
                        {
                            btnNew_Click(null, null);
                            e.Handled = true;
                        }
                        break;
                    case Keys.P:
                        btnPrint_Click(null, null);
                        e.Handled = true;
                        break;
                    case Keys.E:
                        btnExportToExcel_Click(null, null);
                        e.Handled = true;
                        break;
                }
            }
            else
            {
                switch (e.KeyCode)
                {
                    case Keys.F5:
                        btnRefresh_Click(null, null);
                        e.Handled = true;
                        break;
                    case Keys.Enter:
                        if (!_isFormOpening)
                        {
                            btnView_Click(null, null);
                            e.Handled = true;
                        }
                        break;
                    case Keys.Delete:
                        // يمكن إضافة وظيفة الحذف هنا لاحقاً
                        break;
                }
            }
        }

        #endregion

        #region Data Management

        /// <summary>
        /// تحميل بيانات آليات النقل
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                GridControl1.Enabled = false;

                var query = _context.Vehicles
                    .Include(v => v.TransportCompany)
                    .Include(v => v.VehicleType)
                    .Include(v => v.CreatedByUser)
                    .Include(v => v.ModifiedByUser)
                    .AsQueryable();

                // تطبيق البحث إذا كان هناك نص بحث
                var searchText = txtSearch.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    query = query.Where(v =>
                        v.VehicleName.Contains(searchText) ||
                        v.TransportCompany.Name.Contains(searchText) ||
                        v.VehicleType.VehicleTypeName.Contains(searchText) ||
                        (v.Description != null && v.Description.Contains(searchText)));
                }

                var vehicles = await query
                    .OrderBy(v => v.VehicleName)
                    .ToListAsync();

                GridControl1.DataSource = vehicles;

                // تحديث عدد السجلات
                UpdateRecordCount(vehicles.Count);
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات");
            }
            finally
            {
                GridControl1.Enabled = true;
            }
        }

        /// <summary>
        /// تحديث عدد السجلات والإحصائيات
        /// </summary>
        private void UpdateRecordCount(int count)
        {
            try
            {
                // تحديث النص في العنوان ليشمل العدد
                this.Text = $"🚛 إدارة آليات النقل - إجمالي: {count}";
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحديث العدد
            }
        }

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        private void HandleError(Exception ex, string operation)
        {
            string message = $"خطأ في {operation}:\n{ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
            }
            XtraMessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Button Events

        /// <summary>
        /// إنشاء آلية نقل جديدة
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            // منع فتح النافذة إذا كانت مفتوحة بالفعل
            if (_isFormOpening) return;

            try
            {
                _isFormOpening = true; // تعيين العلامة لمنع الفتح المتكرر

                var form = new VehicleAddEditForm();
                var result = form.ShowDialog();

                if (result == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "فتح نموذج إضافة آلية نقل جديدة");
            }
            finally
            {
                _isFormOpening = false; // إعادة تعيين العلامة
            }
        }

        /// <summary>
        /// عرض/تعديل آلية النقل المحددة
        /// </summary>
        private void btnView_Click(object sender, EventArgs e)
        {
            OpenSelectedVehicle();
        }

        /// <summary>
        /// فتح آلية النقل المحددة للتعديل
        /// </summary>
        private void OpenSelectedVehicle()
        {
            // منع التنفيذ إذا كانت نافذة مفتوحة بالفعل
            if (_isFormOpening) return;

            try
            {
                var selectedVehicle = GridView1.GetFocusedRow() as Vehicle;
                if (selectedVehicle == null)
                {
                    XtraMessageBox.Show("يرجى اختيار آلية نقل للعرض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _isFormOpening = true; // تعيين العلامة لمنع الفتح المتكرر

                var form = new VehicleAddEditForm(selectedVehicle);
                var result = form.ShowDialog();

                if (result == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "فتح نموذج تعديل آلية النقل");
            }
            finally
            {
                _isFormOpening = false; // إعادة تعيين العلامة
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadDataAsync();
                XtraMessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحديث البيانات");
            }
        }

        /// <summary>
        /// طباعة البيانات
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                GridControl1.ShowPrintPreview();
            }
            catch (Exception ex)
            {
                HandleError(ex, "طباعة البيانات");
            }
        }

        /// <summary>
        /// تصدير البيانات إلى Excel
        /// </summary>
        private void btnExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx";
                    saveDialog.Title = "حفظ ملف Excel";
                    saveDialog.FileName = $"آليات_النقل_{DateTime.Now:yyyy-MM-dd}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        GridView1.ExportToXlsx(saveDialog.FileName);
                        XtraMessageBox.Show("تم تصدير البيانات بنجاح", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تصدير البيانات");
            }
        }

        #endregion

        #region Search Events

        private void txtSearch_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_isFormOpening)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء البحث المؤقتة
            }
        }

        #endregion

        #region Grid Events

        private async void GridView1_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, new EventArgs());
        }

        private async void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnView_Click(sender, new EventArgs());
            }
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            // تحديث عدد السجلات أو أي معلومات أخرى
        }

        #endregion

        #region Data Methods



        /// <summary>
        /// إعداد أعمدة الشبكة
        /// </summary>
        private void SetupGridColumns()
        {
            try
            {
                GridView1.Columns.Clear();

                // إخفاء الأعمدة غير المرغوب فيها
                GridView1.OptionsView.ShowAutoFilterRow = true;
                GridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
                GridView1.OptionsSelection.MultiSelect = false;

                // إعداد الأعمدة المرئية
                var colVehicleName = GridView1.Columns.AddField("VehicleName");
                colVehicleName.Caption = "اسم الآلية";
                colVehicleName.Visible = true;
                colVehicleName.Width = 150;

                var colTransportCompany = GridView1.Columns.AddField("TransportCompany.Name");
                colTransportCompany.Caption = "شركة النقل";
                colTransportCompany.Visible = true;
                colTransportCompany.Width = 150;

                var colVehicleType = GridView1.Columns.AddField("VehicleType.VehicleTypeName");
                colVehicleType.Caption = "نوع الآلية";
                colVehicleType.Visible = true;
                colVehicleType.Width = 120;

                var colDescription = GridView1.Columns.AddField("Description");
                colDescription.Caption = "الوصف";
                colDescription.Visible = true;
                colDescription.Width = 200;

                var colCreatedAt = GridView1.Columns.AddField("CreatedAt");
                colCreatedAt.Caption = "تاريخ الإنشاء";
                colCreatedAt.Visible = true;
                colCreatedAt.Width = 120;
                colCreatedAt.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreatedAt.DisplayFormat.FormatString = "dd/MM/yyyy";

                var colCreatedBy = GridView1.Columns.AddField("CreatedByUser.Username");
                colCreatedBy.Caption = "أنشئ بواسطة";
                colCreatedBy.Visible = true;
                colCreatedBy.Width = 100;

                // إخفاء باقي الأعمدة
                foreach (DevExpress.XtraGrid.Columns.GridColumn col in GridView1.Columns)
                {
                    if (!col.Visible)
                    {
                        col.OptionsColumn.ShowInCustomizationForm = false;
                    }
                }

                GridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء إعداد الأعمدة
            }
        }

        /// <summary>
        /// الحصول على آلية النقل المحددة
        /// </summary>
        private Vehicle GetSelectedVehicle()
        {
            try
            {
                var selectedRowHandle = GridView1.FocusedRowHandle;
                if (selectedRowHandle >= 0)
                {
                    return GridView1.GetRow(selectedRowHandle) as Vehicle;
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// تحسين عرض الأعمدة
        /// </summary>
        private void OptimizeColumnWidths()
        {
            try
            {
                if (GridView1 != null && GridControl1.Width > 0)
                {
                    GridView1.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحسين العرض
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
