namespace WinFormsApp1.Forms
{
    partial class CustomsDataAddEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            lblCreatedAt = new DevExpress.XtraEditors.LabelControl();
            spinQuantity = new DevExpress.XtraEditors.SpinEdit();
            txtCommonName = new DevExpress.XtraEditors.TextEdit();
            cmbVessel = new DevExpress.XtraEditors.LookUpEdit();
            cmbImporter = new DevExpress.XtraEditors.LookUpEdit();
            cmbMaterial = new DevExpress.XtraEditors.LookUpEdit();
            cmbCustomsType = new DevExpress.XtraEditors.ComboBoxEdit();
            dateCustomsDate = new DevExpress.XtraEditors.DateEdit();
            txtCustomsNumber = new DevExpress.XtraEditors.TextEdit();
            layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem5 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem6 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem7 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem8 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem9 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)layoutControl1).BeginInit();
            layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)spinQuantity.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)txtCommonName.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbVessel.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbImporter.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbMaterial.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbCustomsType.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dateCustomsDate.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)dateCustomsDate.Properties.CalendarTimeProperties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)txtCustomsNumber.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlGroup1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem3).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem4).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem5).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem6).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem7).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem8).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem9).BeginInit();
            SuspendLayout();
            //
            // layoutControl1
            //
            layoutControl1.Controls.Add(lblCreatedAt);
            layoutControl1.Controls.Add(spinQuantity);
            layoutControl1.Controls.Add(txtCommonName);
            layoutControl1.Controls.Add(cmbVessel);
            layoutControl1.Controls.Add(cmbImporter);
            layoutControl1.Controls.Add(cmbMaterial);
            layoutControl1.Controls.Add(cmbCustomsType);
            layoutControl1.Controls.Add(dateCustomsDate);
            layoutControl1.Controls.Add(txtCustomsNumber);
            layoutControl1.Dock = DockStyle.Fill;
            layoutControl1.Location = new Point(0, 0);
            layoutControl1.Name = "layoutControl1";
            layoutControl1.OptionsView.RightToLeftMirroringApplied = true;
            layoutControl1.Root = layoutControlGroup1;
            layoutControl1.Size = new Size(484, 361);
            layoutControl1.TabIndex = 0;
            layoutControl1.Text = "layoutControl1";
            //
            // lblCreatedAt
            // 
            lblCreatedAt.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Italic);
            lblCreatedAt.Appearance.ForeColor = Color.Gray;
            lblCreatedAt.Appearance.Options.UseFont = true;
            lblCreatedAt.Appearance.Options.UseForeColor = true;
            lblCreatedAt.Location = new Point(12, 324);
            lblCreatedAt.Name = "lblCreatedAt";
            lblCreatedAt.Size = new Size(63, 13);
            lblCreatedAt.StyleController = layoutControl1;
            lblCreatedAt.TabIndex = 12;
            lblCreatedAt.Text = "تاريخ الإنشاء";
            lblCreatedAt.Visible = false;
            // 
            // spinQuantity
            // 
            spinQuantity.EditValue = new decimal(new int[] { 1, 0, 0, 262144 });
            spinQuantity.Location = new Point(12, 276);
            spinQuantity.Name = "spinQuantity";
            spinQuantity.Properties.Appearance.Font = new Font("Tahoma", 10F);
            spinQuantity.Properties.Appearance.Options.UseFont = true;
            spinQuantity.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            spinQuantity.Properties.Increment = new decimal(new int[] { 1, 0, 0, 262144 });
            spinQuantity.Properties.MaxValue = new decimal(new int[] { 999999999, 0, 0, 0 });
            spinQuantity.Properties.MinValue = new decimal(new int[] { 1, 0, 0, 262144 });
            spinQuantity.Size = new Size(379, 22);
            spinQuantity.StyleController = layoutControl1;
            spinQuantity.TabIndex = 11;
            // 
            // txtCommonName
            // 
            txtCommonName.Location = new Point(12, 230);
            txtCommonName.Name = "txtCommonName";
            txtCommonName.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtCommonName.Properties.Appearance.Options.UseFont = true;
            txtCommonName.Properties.MaxLength = 100;
            txtCommonName.Size = new Size(379, 22);
            txtCommonName.StyleController = layoutControl1;
            txtCommonName.TabIndex = 10;
            // 
            // cmbVessel
            // 
            cmbVessel.Location = new Point(12, 184);
            cmbVessel.Name = "cmbVessel";
            cmbVessel.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbVessel.Properties.Appearance.Options.UseFont = true;
            cmbVessel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbVessel.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] { new DevExpress.XtraEditors.Controls.LookUpColumnInfo("VesselName", "اسم الباخرة") });
            cmbVessel.Properties.DisplayMember = "VesselName";
            cmbVessel.Properties.NullText = "اختر الباخرة...";
            cmbVessel.Properties.ValueMember = "VesselId";
            cmbVessel.Size = new Size(379, 22);
            cmbVessel.StyleController = layoutControl1;
            cmbVessel.TabIndex = 9;
            // 
            // cmbImporter
            // 
            cmbImporter.Location = new Point(12, 138);
            cmbImporter.Name = "cmbImporter";
            cmbImporter.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbImporter.Properties.Appearance.Options.UseFont = true;
            cmbImporter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbImporter.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] { new DevExpress.XtraEditors.Controls.LookUpColumnInfo("Name", "اسم المستورد") });
            cmbImporter.Properties.DisplayMember = "Name";
            cmbImporter.Properties.NullText = "اختر المستورد...";
            cmbImporter.Properties.ValueMember = "ImporterId";
            cmbImporter.Size = new Size(379, 22);
            cmbImporter.StyleController = layoutControl1;
            cmbImporter.TabIndex = 8;
            // 
            // cmbMaterial
            // 
            cmbMaterial.Location = new Point(12, 92);
            cmbMaterial.Name = "cmbMaterial";
            cmbMaterial.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbMaterial.Properties.Appearance.Options.UseFont = true;
            cmbMaterial.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbMaterial.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] { new DevExpress.XtraEditors.Controls.LookUpColumnInfo("MaterialName", "اسم المادة"), new DevExpress.XtraEditors.Controls.LookUpColumnInfo("UnitType", "نوع الوحدة") });
            cmbMaterial.Properties.DisplayMember = "MaterialName";
            cmbMaterial.Properties.NullText = "اختر المادة...";
            cmbMaterial.Properties.ValueMember = "MaterialId";
            cmbMaterial.Size = new Size(379, 22);
            cmbMaterial.StyleController = layoutControl1;
            cmbMaterial.TabIndex = 7;
            // 
            // cmbCustomsType
            // 
            cmbCustomsType.Location = new Point(12, 46);
            cmbCustomsType.Name = "cmbCustomsType";
            cmbCustomsType.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbCustomsType.Properties.Appearance.Options.UseFont = true;
            cmbCustomsType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbCustomsType.Properties.Items.AddRange(new object[] { "استيراد", "تصدير", "ترانزيت", "إعادة تصدير" });
            cmbCustomsType.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            cmbCustomsType.Size = new Size(189, 22);
            cmbCustomsType.StyleController = layoutControl1;
            cmbCustomsType.TabIndex = 6;
            // 
            // dateCustomsDate
            // 
            dateCustomsDate.EditValue = null;
            dateCustomsDate.Location = new Point(205, 46);
            dateCustomsDate.Name = "dateCustomsDate";
            dateCustomsDate.Properties.Appearance.Font = new Font("Tahoma", 10F);
            dateCustomsDate.Properties.Appearance.Options.UseFont = true;
            dateCustomsDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            dateCustomsDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            dateCustomsDate.Properties.DisplayFormat.FormatString = "yyyy/MM/dd";
            dateCustomsDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            dateCustomsDate.Properties.EditFormat.FormatString = "yyyy/MM/dd";
            dateCustomsDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            dateCustomsDate.Properties.MaskSettings.Set("mask", "yyyy/MM/dd");
            dateCustomsDate.Size = new Size(186, 22);
            dateCustomsDate.StyleController = layoutControl1;
            dateCustomsDate.TabIndex = 5;
            // 
            // txtCustomsNumber
            // 
            txtCustomsNumber.Location = new Point(12, 0);
            txtCustomsNumber.Name = "txtCustomsNumber";
            txtCustomsNumber.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtCustomsNumber.Properties.Appearance.Options.UseFont = true;
            txtCustomsNumber.Properties.MaxLength = 10;
            txtCustomsNumber.Size = new Size(379, 22);
            txtCustomsNumber.StyleController = layoutControl1;
            txtCustomsNumber.TabIndex = 4;
            // 
            // layoutControlGroup1
            // 
            layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            layoutControlGroup1.GroupBordersVisible = false;
            layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] { layoutControlItem1, layoutControlItem2, layoutControlItem3, layoutControlItem4, layoutControlItem5, layoutControlItem6, layoutControlItem7, layoutControlItem8, layoutControlItem9 });
            layoutControlGroup1.Name = "layoutControlGroup1";
            layoutControlGroup1.Padding = new DevExpress.XtraLayout.Utils.Padding(0, 0, 0, 0);
            layoutControlGroup1.Size = new Size(484, 361);
            layoutControlGroup1.TextVisible = false;
            // 
            // layoutControlItem1
            // 
            layoutControlItem1.Control = txtCustomsNumber;
            layoutControlItem1.Location = new Point(0, 0);
            layoutControlItem1.Name = "layoutControlItem1";
            layoutControlItem1.Size = new Size(484, 46);
            layoutControlItem1.Text = "🔢 رقم الجمرك:";
            layoutControlItem1.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem1.TextSize = new Size(67, 13);
            // 
            // layoutControlItem2
            // 
            layoutControlItem2.Control = dateCustomsDate;
            layoutControlItem2.Location = new Point(193, 46);
            layoutControlItem2.Name = "layoutControlItem2";
            layoutControlItem2.Size = new Size(291, 46);
            layoutControlItem2.Text = "📅 تاريخ الجمرك:";
            layoutControlItem2.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem2.TextSize = new Size(67, 13);
            // 
            // layoutControlItem3
            // 
            layoutControlItem3.Control = cmbCustomsType;
            layoutControlItem3.Location = new Point(0, 46);
            layoutControlItem3.Name = "layoutControlItem3";
            layoutControlItem3.Size = new Size(193, 46);
            layoutControlItem3.Text = "📋 نوع الجمرك:";
            layoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem3.TextSize = new Size(67, 13);
            // 
            // layoutControlItem4
            // 
            layoutControlItem4.Control = cmbMaterial;
            layoutControlItem4.Location = new Point(0, 92);
            layoutControlItem4.Name = "layoutControlItem4";
            layoutControlItem4.Size = new Size(484, 46);
            layoutControlItem4.Text = "📦 المادة:";
            layoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem4.TextSize = new Size(67, 13);
            // 
            // layoutControlItem5
            // 
            layoutControlItem5.Control = cmbImporter;
            layoutControlItem5.Location = new Point(0, 138);
            layoutControlItem5.Name = "layoutControlItem5";
            layoutControlItem5.Size = new Size(484, 46);
            layoutControlItem5.Text = "🏢 المستورد:";
            layoutControlItem5.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem5.TextSize = new Size(67, 13);
            // 
            // layoutControlItem6
            // 
            layoutControlItem6.Control = cmbVessel;
            layoutControlItem6.Location = new Point(0, 184);
            layoutControlItem6.Name = "layoutControlItem6";
            layoutControlItem6.Size = new Size(484, 46);
            layoutControlItem6.Text = "🚢 الباخرة:";
            layoutControlItem6.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem6.TextSize = new Size(67, 13);
            // 
            // layoutControlItem7
            // 
            layoutControlItem7.Control = txtCommonName;
            layoutControlItem7.Location = new Point(0, 230);
            layoutControlItem7.Name = "layoutControlItem7";
            layoutControlItem7.Size = new Size(484, 46);
            layoutControlItem7.Text = "🏷️ الاسم الشائع:";
            layoutControlItem7.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem7.TextSize = new Size(67, 13);
            // 
            // layoutControlItem8
            // 
            layoutControlItem8.Control = spinQuantity;
            layoutControlItem8.Location = new Point(0, 276);
            layoutControlItem8.Name = "layoutControlItem8";
            layoutControlItem8.Size = new Size(484, 46);
            layoutControlItem8.Text = "⚖️ الكمية:";
            layoutControlItem8.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem8.TextSize = new Size(67, 13);
            //
            // layoutControlItem9
            //
            layoutControlItem9.Control = lblCreatedAt;
            layoutControlItem9.Location = new Point(0, 322);
            layoutControlItem9.Name = "layoutControlItem9";
            layoutControlItem9.Size = new Size(484, 39);
            layoutControlItem9.TextSize = new Size(0, 0);
            layoutControlItem9.TextVisible = false;
            //
            // CustomsDataAddEditForm
            //
            AutoScaleDimensions = new SizeF(6F, 13F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(484, 361);
            Controls.Add(layoutControl1);
            Name = "CustomsDataAddEditForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterParent;
            Text = "إدارة البيانات الجمركية";
            ((System.ComponentModel.ISupportInitialize)layoutControl1).EndInit();
            layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)spinQuantity.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)txtCommonName.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbVessel.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbImporter.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbMaterial.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbCustomsType.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)dateCustomsDate.Properties.CalendarTimeProperties).EndInit();
            ((System.ComponentModel.ISupportInitialize)dateCustomsDate.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)txtCustomsNumber.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlGroup1).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem1).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem2).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem3).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem4).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem5).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem6).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem7).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem8).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem9).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraEditors.LabelControl lblCreatedAt;
        private DevExpress.XtraEditors.SpinEdit spinQuantity;
        private DevExpress.XtraEditors.TextEdit txtCommonName;
        private DevExpress.XtraEditors.LookUpEdit cmbVessel;
        private DevExpress.XtraEditors.LookUpEdit cmbImporter;
        private DevExpress.XtraEditors.LookUpEdit cmbMaterial;
        private DevExpress.XtraEditors.ComboBoxEdit cmbCustomsType;
        private DevExpress.XtraEditors.DateEdit dateCustomsDate;
        private DevExpress.XtraEditors.TextEdit txtCustomsNumber;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem5;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem6;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem7;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem8;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem9;
    }
}
