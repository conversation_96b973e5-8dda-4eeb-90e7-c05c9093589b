using DevExpress.XtraBars;
using DevExpress.XtraBars.FluentDesignSystem;
using DevExpress.XtraBars.Navigation;
using WinFormsApp1.Forms;

namespace WinFormsApp1.Forms
{
    public partial class MainForm : FluentDesignForm
    {
        public MainForm()
        {
            InitializeComponent();
            InitializeNavigation();
        }

        private void InitializeNavigation()
        {
            // إعداد عناصر التنقل
            CreateNavigationElements();

            // إظهار الشاشة الرئيسية عند البدء
            ShowHomeScreen();
        }

        private void CreateNavigationElements()
        {
            // إنشاء عناصر التنقل بدون مجموعات

            // الشاشة الرئيسية
            var homeElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "الشاشة الرئيسية",
                Name = "navHome"
            };

            // البيانات الأساسية
            var usersElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "المستخدمين",
                Name = "navUsers"
            };

            var currenciesElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "العملات",
                Name = "navCurrencies"
            };

            var materialsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "المواد",
                Name = "navMaterials"
            };

            var vesselsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "البواخر",
                Name = "navVessels"
            };

            var partiesElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "المتعاملين",
                Name = "navParties"
            };

            // العمليات
            var importersElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "المستوردين",
                Name = "navImporters"
            };

            var merchantsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "التجار",
                Name = "navMerchants"
            };

            var customsDataElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "البيانات الجمركية",
                Name = "navCustomsData"
            };

            var warehouseTransactionsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "حركات المستودع",
                Name = "navWarehouseTransactions"
            };

            // النقل والتوزيع
            var transportCompaniesElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "شركات النقل",
                Name = "navTransportCompanies"
            };

            var vehiclesElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "آليات النقل",
                Name = "navVehicles"
            };

            var regionsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "المناطق",
                Name = "navRegions"
            };

            var distributorsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "الموزعين",
                Name = "navDistributors"
            };

            // المستودعات
            var warehousesElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "إدارة المستودعات",
                Name = "navWarehouses"
            };

            var receiptStatementsElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "كشوف التقبيق",
                Name = "navReceiptStatements"
            };

            // التقارير
            var inventoryReportElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "تقرير المخزون",
                Name = "navInventoryReport"
            };

            var transactionReportElement = new AccordionControlElement(ElementStyle.Item)
            {
                Text = "تقرير الحركات",
                Name = "navTransactionReport"
            };

            // إضافة جميع العناصر مباشرة إلى التنقل
            accordionControl1.Elements.AddRange(new[] {
                homeElement,
                usersElement,
                currenciesElement,
                materialsElement,
                vesselsElement,
                partiesElement,
                importersElement,
                merchantsElement,
                customsDataElement,
                warehouseTransactionsElement,
                transportCompaniesElement,
                vehiclesElement,
                regionsElement,
                distributorsElement,
                warehousesElement,
                receiptStatementsElement,
                inventoryReportElement,
                transactionReportElement
            });

            // ربط أحداث النقر
            accordionControl1.ElementClick += AccordionControl1_ElementClick;
        }

        private void AccordionControl1_ElementClick(object sender, ElementClickEventArgs e)
        {
            // التعامل مع النقر على عناصر التنقل
            switch (e.Element.Name)
            {
                case "navHome":
                    ShowHomeScreen();
                    break;

                case "navUsers":
                    ShowForm(new UsersForm());
                    break;

                case "navMaterials":
                    ShowForm(new MaterialsForm());
                    break;

                case "navCurrencies":
                    ShowForm(new CurrenciesForm());
                    break;

                case "navVessels":
                    ShowForm(new VesselsForm());
                    break;

                case "navParties":
                    ShowForm(new PartiesForm());
                    break;

                case "navImporters":
                    ShowForm(new ImportersForm());
                    break;

                case "navMerchants":
                    ShowForm(new MerchantsForm());
                    break;

                case "navTransportCompanies":
                    ShowForm(new TransportCompaniesForm());
                    break;

                case "navCustomsData":
                    ShowForm(new CustomsDataForm());
                    break;

                case "navWarehouseTransactions":
                    ShowMessage("سيتم فتح شاشة حركات المستودع");
                    break;
                case "navVehicles":
                    try
                    {
                        ShowForm(new VehiclesForm());
                    }
                    catch (Exception ex)
                    {
                        string message = $"خطأ في فتح شاشة آليات النقل:\n{ex.Message}";
                        if (ex.InnerException != null)
                        {
                            message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                        }
                        message += $"\n\nStack Trace:\n{ex.StackTrace}";
                        MessageBox.Show(message, "خطأ في فتح شاشة آليات النقل", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    break;
                case "navRegions":
                    ShowMessage("سيتم فتح شاشة المناطق");
                    break;
                case "navDistributors":
                    ShowMessage("سيتم فتح شاشة الموزعين");
                    break;
                case "navWarehouses":
                    ShowForm(new WarehousesForm());
                    break;
                case "navReceiptStatements":
                    ShowMessage("سيتم فتح شاشة كشوف التقبيق");
                    break;
                case "navInventoryReport":
                    ShowMessage("سيتم فتح تقرير المخزون");
                    break;
                case "navTransactionReport":
                    ShowMessage("سيتم فتح تقرير الحركات");
                    break;
            }
        }

        private void ShowHomeScreen()
        {
            // إظهار الشاشة الرئيسية
            ClearMainContent();

            // إضافة رسالة ترحيب محدثة
            var welcomeLabel = new DevExpress.XtraEditors.LabelControl
            {
                Anchor = AnchorStyles.None,
                Font = new Font("Tahoma", 24F, FontStyle.Bold),
                ForeColor = Color.Gray,
                AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None,
                Location = new Point(200, 300),
                Size = new Size(600, 100),
                Text = "مرحباً بك في نظام إدارة المستودعات\r\nاختر من القائمة الجانبية للبدء",
                Appearance = { TextOptions = { HAlignment = DevExpress.Utils.HorzAlignment.Center } }
            };

            fluentDesignFormContainer1.Controls.Add(welcomeLabel);
        }

        private void ClearMainContent()
        {
            // مسح المحتوى الحالي
            fluentDesignFormContainer1.Controls.Clear();
        }

        private void ShowForm(Form form)
        {
            try
            {
                // مسح المحتوى الحالي
                ClearMainContent();

                // إعداد النموذج ليكون جزءاً من الحاوية
                form.TopLevel = false;
                form.FormBorderStyle = FormBorderStyle.None;
                form.Dock = DockStyle.Fill;

                // إضافة النموذج إلى الحاوية
                fluentDesignFormContainer1.Controls.Add(form);
                form.Show();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في فتح النموذج:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                message += $"\n\nStack Trace:\n{ex.StackTrace}";
                MessageBox.Show(message, "خطأ في فتح النموذج", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowMessage(string message)
        {
            DevExpress.XtraEditors.XtraMessageBox.Show(message, "نظام إدارة المستودعات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
