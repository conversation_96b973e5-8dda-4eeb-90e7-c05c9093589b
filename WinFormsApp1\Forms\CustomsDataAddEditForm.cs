using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل البيانات الجمركية
    /// </summary>
    public partial class CustomsDataAddEditForm : MasterF
    {
        #region Fields

        private new WarehouseDbContext? _context;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة بيان جمركي جديد
        /// </summary>
        public CustomsDataAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "📋 إدارة البيانات الجمركية - إضافة جديد";

                // تهيئة قاعدة البيانات
                _context = DatabaseService.GetDbContext();

                _currentEntity = CreateNewEntityInstance();
                SetMode(FormMode.Add);
                LoadEntityToControls();

                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل بيان جمركي موجود
        /// </summary>
        public CustomsDataAddEditForm(CustomsD selectedCustomsData)
        {
            try
            {
                InitializeComponent();
                this.Text = "📋 إدارة البيانات الجمركية - تعديل";

                // تهيئة قاعدة البيانات
                _context = DatabaseService.GetDbContext();

                _currentEntity = selectedCustomsData;
                SetMode(FormMode.Edit);
                LoadEntityToControls();

                // تحميل البيانات للتنقل
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل البيانات للتنقل
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                _dataList = await LoadEntitiesFromDatabase();
                LoadReferenceDataSafe();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        /// <summary>
        /// تحميل البيانات المرجعية بشكل آمن
        /// </summary>
        private void LoadReferenceDataSafe()
        {
            try
            {
                if (_context == null) return;

                // تحميل المواد
                var materials = _context.Materials.OrderBy(m => m.MaterialName).ToList();
                cmbMaterial.Properties.DataSource = materials;
                cmbMaterial.Properties.DisplayMember = "MaterialName";
                cmbMaterial.Properties.ValueMember = "MaterialId";

                // تحميل المستوردين
                var importers = _context.Importers.OrderBy(i => i.Name).ToList();
                cmbImporter.Properties.DataSource = importers;
                cmbImporter.Properties.DisplayMember = "Name";
                cmbImporter.Properties.ValueMember = "ImporterId";

                // تحميل البواخر
                var vessels = _context.Vessels.OrderBy(v => v.VesselName).ToList();
                cmbVessel.Properties.DataSource = vessels;
                cmbVessel.Properties.DisplayMember = "VesselName";
                cmbVessel.Properties.ValueMember = "VesselId";

                // إعداد قائمة أنواع الجمرك
                cmbCustomsType.Properties.Items.Clear();
                cmbCustomsType.Properties.Items.AddRange(new string[]
                {
                    "استيراد",
                    "تصدير",
                    "ترانزيت",
                    "إعادة تصدير"
                });
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات المرجعية");
            }
        }

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                if (_context == null) return new List<object>();

                var customsData = await _context.CustomsDs
                    .Include(c => c.Material)
                    .Include(c => c.Importer)
                    .Include(c => c.Vessel)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();

                return customsData.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات الجمركية من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new CustomsD
            {
                CustomsDate = DateOnly.FromDateTime(DateTime.Now),
                CreatedAt = DateTime.Now,
                Quantity = 1.0000m
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            try
            {
                if (_currentEntity is CustomsD customsData)
                {
                    txtCustomsNumber.Text = customsData.CustomsNumber ?? "";

                    if (customsData.CustomsDate != default(DateOnly))
                    {
                        dateCustomsDate.EditValue = customsData.CustomsDate.ToDateTime(TimeOnly.MinValue);
                    }
                    else
                    {
                        dateCustomsDate.EditValue = DateTime.Now;
                    }

                    cmbCustomsType.Text = customsData.CustomsType ?? "";
                    txtCommonName.Text = customsData.CommonName ?? "";
                    spinQuantity.Value = customsData.Quantity;

                    // تعيين القيم للقوائم المنسدلة
                    if (customsData.MaterialId > 0)
                        cmbMaterial.EditValue = customsData.MaterialId;
                    else
                        cmbMaterial.EditValue = null;

                    if (customsData.ImporterId > 0)
                        cmbImporter.EditValue = customsData.ImporterId;
                    else
                        cmbImporter.EditValue = null;

                    if (customsData.VesselId > 0)
                        cmbVessel.EditValue = customsData.VesselId;
                    else
                        cmbVessel.EditValue = null;

                    // عرض تاريخ الإنشاء
                    if (customsData.CustomsDataId > 0)
                    {
                        lblCreatedAt.Text = $"تاريخ الإنشاء: {customsData.CreatedAt:yyyy/MM/dd HH:mm}";
                        lblCreatedAt.Visible = true;
                    }
                    else
                    {
                        lblCreatedAt.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات إلى عناصر التحكم");
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is CustomsD customsData)
            {
                customsData.CustomsNumber = txtCustomsNumber.Text.Trim();

                // التحقق من التاريخ
                if (dateCustomsDate.EditValue != null)
                {
                    customsData.CustomsDate = DateOnly.FromDateTime((DateTime)dateCustomsDate.EditValue);
                }
                else
                {
                    customsData.CustomsDate = DateOnly.FromDateTime(DateTime.Now);
                }

                customsData.CustomsType = cmbCustomsType.Text.Trim();
                customsData.CommonName = string.IsNullOrWhiteSpace(txtCommonName.Text) ? null : txtCommonName.Text.Trim();
                customsData.Quantity = spinQuantity.Value;

                // تعيين المعرفات من القوائم المنسدلة مع التحقق
                if (cmbMaterial.EditValue != null && int.TryParse(cmbMaterial.EditValue.ToString(), out int materialId))
                {
                    customsData.MaterialId = materialId;
                }
                else
                {
                    customsData.MaterialId = 0; // سيتم التحقق من هذا في SaveEntityToDatabase
                }

                if (cmbImporter.EditValue != null && int.TryParse(cmbImporter.EditValue.ToString(), out int importerId))
                {
                    customsData.ImporterId = importerId;
                }
                else
                {
                    customsData.ImporterId = 0; // سيتم التحقق من هذا في SaveEntityToDatabase
                }

                if (cmbVessel.EditValue != null && int.TryParse(cmbVessel.EditValue.ToString(), out int vesselId))
                {
                    customsData.VesselId = vesselId;
                }
                else
                {
                    customsData.VesselId = 0; // سيتم التحقق من هذا في SaveEntityToDatabase
                }

                // تعيين تاريخ الإنشاء للبيانات الجديدة
                if (customsData.CustomsDataId == 0)
                {
                    customsData.CreatedAt = DateTime.Now;
                }
                else
                {
                    customsData.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            try
            {
                // التحقق من رقم الجمرك
                if (string.IsNullOrWhiteSpace(txtCustomsNumber.Text))
                {
                    XtraMessageBox.Show("يرجى إدخال رقم الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCustomsNumber.Focus();
                    return false;
                }

                // التحقق من تاريخ الجمرك
                if (dateCustomsDate.EditValue == null)
                {
                    XtraMessageBox.Show("يرجى إدخال تاريخ الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dateCustomsDate.Focus();
                    return false;
                }

                // التحقق من نوع الجمرك
                if (string.IsNullOrWhiteSpace(cmbCustomsType.Text))
                {
                    XtraMessageBox.Show("يرجى اختيار نوع الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbCustomsType.Focus();
                    return false;
                }

                // التحقق من المادة
                if (cmbMaterial.EditValue == null || !int.TryParse(cmbMaterial.EditValue.ToString(), out int materialId) || materialId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار المادة", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbMaterial.Focus();
                    return false;
                }

                // التحقق من المستورد
                if (cmbImporter.EditValue == null || !int.TryParse(cmbImporter.EditValue.ToString(), out int importerId) || importerId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار المستورد", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbImporter.Focus();
                    return false;
                }

                // التحقق من الباخرة
                if (cmbVessel.EditValue == null || !int.TryParse(cmbVessel.EditValue.ToString(), out int vesselId) || vesselId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار الباخرة", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbVessel.Focus();
                    return false;
                }

                // التحقق من الكمية
                if (spinQuantity.Value <= 0)
                {
                    XtraMessageBox.Show("يرجى إدخال كمية صحيحة أكبر من صفر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    spinQuantity.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// حفظ الكائن في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            try
            {
                if (entity is CustomsD customsData && _context != null)
                {
                    // التحقق من تكرار رقم الجمرك
                    var customsNumber = txtCustomsNumber.Text.Trim();

                    if (!string.IsNullOrEmpty(customsNumber))
                    {
                        var existingCustomsData = await _context.CustomsDs
                            .Where(c => c.CustomsNumber == customsNumber && c.CustomsDataId != customsData.CustomsDataId)
                            .FirstOrDefaultAsync();

                        if (existingCustomsData != null)
                        {
                            XtraMessageBox.Show($"رقم الجمرك '{customsNumber}' موجود مسبقاً", "تكرار في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtCustomsNumber.Focus();
                            return false;
                        }
                    }

                    // التأكد من وجود القيم المطلوبة
                    if (customsData.MaterialId <= 0)
                    {
                        XtraMessageBox.Show("يرجى اختيار المادة", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        cmbMaterial.Focus();
                        return false;
                    }

                    if (customsData.ImporterId <= 0)
                    {
                        XtraMessageBox.Show("يرجى اختيار المستورد", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        cmbImporter.Focus();
                        return false;
                    }

                    if (customsData.VesselId <= 0)
                    {
                        XtraMessageBox.Show("يرجى اختيار الباخرة", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        cmbVessel.Focus();
                        return false;
                    }

                    if (customsData.CustomsDataId == 0)
                    {
                        customsData.CreatedAt = DateTime.Now;
                        _context.CustomsDs.Add(customsData);
                    }
                    else
                    {
                        customsData.ModifiedAt = DateTime.Now;
                        _context.CustomsDs.Update(customsData);
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }

                XtraMessageBox.Show("خطأ في البيانات أو الاتصال بقاعدة البيانات", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في حفظ البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل الخطأ: {ex.InnerException.Message}";
                }
                XtraMessageBox.Show(errorMessage, "خطأ في الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// حذف الكائن من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            try
            {
                if (entity is CustomsD customsData && customsData.CustomsDataId > 0 && _context != null)
                {
                    _context.CustomsDs.Remove(customsData);
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف البيان الجمركي");
                return false;
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
