﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using WinFormsApp1.Data;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// حالات النموذج المختلفة
    /// </summary>
    public enum FormMode
    {
        /// <summary>وضع الإضافة</summary>
        Add,
        /// <summary>وضع التعديل</summary>
        Edit,
        /// <summary>وضع العرض فقط</summary>
        View
    }

    /// <summary>
    /// فورم أساسي محسن للوراثة مع وظائف CRUD
    /// </summary>
    public partial class MasterF : DevExpress.XtraEditors.XtraForm
    {
        #region Fields & Properties

        protected WarehouseDbContext? _context;
        protected object? _currentEntity;
        protected List<object> _dataList = new List<object>();
        protected int _currentIndex = 0;

        /// <summary>
        /// حالة النموذج الحالية
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public FormMode CurrentMode { get; protected set; } = FormMode.Add;

        /// <summary>
        /// يحدد ما إذا كان النموذج في وضع القراءة فقط
        /// </summary>
        public bool IsReadOnly => CurrentMode == FormMode.View;

        /// <summary>
        /// يحدد ما إذا كان هناك بيانات محملة
        /// </summary>
        public bool HasData => _dataList.Count > 0;

        /// <summary>
        /// العنصر الحالي المحدد
        /// </summary>
        public object? CurrentEntity => _currentEntity;

        #endregion

        #region Constructor

        protected MasterF()
        {
            InitializeComponent();

            // تجنب تهيئة قاعدة البيانات في Design Mode
            if (!DesignMode)
            {
                try
                {
                    _context = DatabaseService.GetDbContext();
                }
                catch (Exception ex)
                {
                    // في حالة فشل تهيئة قاعدة البيانات، نستخدم context فارغ
                    System.Diagnostics.Debug.WriteLine($"Failed to initialize database context: {ex.Message}");
                    _context = null;
                }

                SetupBarEvents();
                SetupForm();
            }
        }

        #endregion

        #region Setup Methods

        /// <summary>
        /// إعداد النموذج الأساسي
        /// </summary>
        protected virtual void SetupForm()
        {
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterParent;
            this.KeyPreview = true;
            this.KeyDown += OnFormKeyDown;

            // تفعيل الانتقال للحقل التالي عند الضغط على Enter
            EnableEnterMoveNext();

            // إعداد النصوص الافتراضية للأزرار
            SetupButtonCaptions();
        }

        /// <summary>
        /// تفعيل الانتقال للحقل التالي عند الضغط على Enter
        /// </summary>
        protected virtual void EnableEnterMoveNext()
        {
            try
            {
                // البحث عن جميع عناصر التحكم في النموذج
                EnableEnterMoveNextForControls(this.Controls);
            }
            catch (Exception ex)
            {
                // في حالة حدوث خطأ، نتجاهله لعدم تعطيل النموذج
                System.Diagnostics.Debug.WriteLine($"خطأ في تفعيل Enter Move Next: {ex.Message}");
            }
        }

        /// <summary>
        /// تفعيل Enter Move Next لمجموعة من عناصر التحكم
        /// </summary>
        /// <param name="controls">مجموعة عناصر التحكم</param>
        private void EnableEnterMoveNextForControls(Control.ControlCollection controls)
        {
            foreach (Control control in controls)
            {
                // تفعيل Enter Move Next للعناصر المناسبة
                if (control is DevExpress.XtraEditors.TextEdit textEdit)
                {
                    textEdit.Properties.KeyDown += TextEdit_KeyDown;
                }
                else if (control is DevExpress.XtraEditors.SpinEdit spinEdit)
                {
                    spinEdit.Properties.KeyDown += SpinEdit_KeyDown;
                }
                else if (control is DevExpress.XtraEditors.ComboBoxEdit comboEdit)
                {
                    comboEdit.Properties.KeyDown += ComboEdit_KeyDown;
                }
                else if (control is DevExpress.XtraEditors.DateEdit dateEdit)
                {
                    dateEdit.Properties.KeyDown += DateEdit_KeyDown;
                }
                else if (control is DevExpress.XtraEditors.MemoEdit memoEdit)
                {
                    // للـ MemoEdit نحتاج معالجة خاصة لأنه متعدد الأسطر
                    memoEdit.Properties.KeyDown += MemoEdit_KeyDown;
                }

                // البحث في العناصر الفرعية (مثل LayoutControl, PanelControl, إلخ)
                if (control.HasChildren)
                {
                    EnableEnterMoveNextForControls(control.Controls);
                }
            }
        }

        /// <summary>
        /// معالج Enter للـ TextEdit
        /// </summary>
        private void TextEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج Enter للـ SpinEdit
        /// </summary>
        private void SpinEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج Enter للـ ComboBoxEdit
        /// </summary>
        private void ComboEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج Enter للـ DateEdit
        /// </summary>
        private void DateEdit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SendKeys.Send("{TAB}");
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج Enter للـ MemoEdit (معالجة خاصة)
        /// </summary>
        private void MemoEdit_KeyDown(object sender, KeyEventArgs e)
        {
            // للـ MemoEdit، ننتقل للحقل التالي فقط عند Ctrl+Enter
            if (e.KeyCode == Keys.Enter && e.Control)
            {
                SendKeys.Send("{TAB}");
                e.Handled = true;
            }
            // Enter العادي يبقى لإنشاء سطر جديد في النص
        }

        /// <summary>
        /// إعداد نصوص الأزرار
        /// </summary>
        protected virtual void SetupButtonCaptions()
        {
            barButtonItem_New.Caption = "🆕 جديد";
            barButtonItem_Save.Caption = "💾 حفظ";
            barButtonItem_Delete.Caption = "🗑️ حذف";
            barButtonItem_Print.Caption = "🖨️ طباعة";

            barButtonItem4.Caption = "⏮️ الأول";
            barButtonItem2.Caption = "⏪ السابق";
            barButtonItem3.Caption = "⏩ التالي";
            barButtonItem1.Caption = "⏭️ الأخير";

            // إعداد التلميحات
            barButtonItem_New.Hint = "إنشاء عنصر جديد (Ctrl+N)";
            barButtonItem_Save.Hint = "حفظ التغييرات (Ctrl+S)";
            barButtonItem_Delete.Hint = "حذف العنصر الحالي (Delete)";
            barButtonItem_Print.Hint = "طباعة العنصر (Ctrl+P)";
        }

        private void SetupBarEvents()
        {
            // أزرار العمليات
            barButtonItem_New.ItemClick += BarButtonItem_New_ItemClick;
            barButtonItem_Save.ItemClick += BarButtonItem_Save_ItemClick;
            barButtonItem_Delete.ItemClick += BarButtonItem_Delete_ItemClick;
            barButtonItem_Print.ItemClick += BarButtonItem_Print_ItemClick;

            // أزرار التنقل
            barButtonItem4.ItemClick += BarButtonItem4_ItemClick; // First
            barButtonItem2.ItemClick += BarButtonItem2_ItemClick; // Previous
            barButtonItem3.ItemClick += BarButtonItem3_ItemClick; // Next
            barButtonItem1.ItemClick += BarButtonItem1_ItemClick; // Last

            barEditItem1.EditValueChanged += BarEditItem1_EditValueChanged;
        }

        #endregion

        #region Virtual Methods - يمكن تجاوزها في الفورم المشتق

        /// <summary>
        /// تحميل بيانات الكائن في عناصر التحكم
        /// </summary>
        protected virtual void LoadEntityToControls()
        {
            // تنفيذ افتراضي فارغ
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم في الكائن
        /// </summary>
        protected virtual void LoadControlsToEntity()
        {
            // تنفيذ افتراضي فارغ
        }

        /// <summary>
        /// التحقق من صحة البيانات المدخلة
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        protected virtual bool ValidateEntityData()
        {
            return true;
        }

        /// <summary>
        /// تحميل قائمة البيانات من قاعدة البيانات
        /// </summary>
        /// <returns>قائمة البيانات</returns>
        protected virtual async Task<List<object>> LoadEntitiesFromDatabase()
        {
            return new List<object>();
        }

        /// <summary>
        /// حفظ الكائن في قاعدة البيانات
        /// </summary>
        /// <param name="entity">الكائن المراد حفظه</param>
        /// <returns>true إذا تم الحفظ بنجاح</returns>
        protected virtual async Task<bool> SaveEntityToDatabase(object entity)
        {
            return false;
        }

        /// <summary>
        /// حذف الكائن من قاعدة البيانات
        /// </summary>
        /// <param name="entity">الكائن المراد حذفه</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        protected virtual async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            return false;
        }

        #endregion

        #region Virtual Methods - يمكن تجاوزها في الفورم المشتق

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        /// <returns>الكائن الجديد</returns>
        protected virtual object CreateNewEntityInstance()
        {
            // تطبيق افتراضي - يجب على الكلاسات الفرعية تجاوز هذه الدالة
            throw new NotImplementedException("يجب على الكلاس الفرعي تطبيق دالة CreateNewEntityInstance");
        }

        /// <summary>
        /// تطبيق وضع القراءة فقط على عناصر التحكم
        /// </summary>
        /// <param name="readOnly">true للقراءة فقط</param>
        protected virtual void SetControlsReadOnly(bool readOnly)
        {
            // يمكن تجاوزها في الفورم المشتق
        }

        /// <summary>
        /// طباعة الكائن الحالي
        /// </summary>
        protected virtual void PrintCurrentEntity()
        {
            if (_currentEntity != null)
            {
                XtraMessageBox.Show("وظيفة الطباعة غير مُعرَّفة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        /// <summary>
        /// تخصيص رسالة التأكيد للحذف
        /// </summary>
        /// <returns>نص رسالة التأكيد</returns>
        protected virtual string GetDeleteConfirmationMessage()
        {
            return "هل أنت متأكد من حذف هذا العنصر؟";
        }

        /// <summary>
        /// تخصيص رسالة نجاح الحفظ
        /// </summary>
        /// <param name="isEdit">true إذا كان تعديل، false إذا كان إضافة</param>
        /// <returns>نص رسالة النجاح</returns>
        protected virtual string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث البيانات بنجاح" : "تم إضافة البيانات بنجاح";
        }

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        /// <param name="ex">الاستثناء</param>
        /// <param name="operation">نوع العملية</param>
        protected virtual void HandleError(Exception ex, string operation)
        {
            XtraMessageBox.Show($"خطأ في {operation}: {ex.Message}", "خطأ",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Data Management Methods

        /// <summary>
        /// تحميل البيانات وتهيئة النموذج
        /// </summary>
        public virtual async Task InitializeFormAsync()
        {
            try
            {
                await LoadDataListAsync();
                if (HasData)
                {
                    NavigateToRecord(0);
                    SetMode(FormMode.Edit);
                }
                else
                {
                    CreateNewRecord();
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات");
            }
        }

        /// <summary>
        /// تحميل قائمة البيانات
        /// </summary>
        public virtual async Task LoadDataListAsync()
        {
            try
            {
                _dataList = await LoadEntitiesFromDatabase();
                UpdateNavigationInfo();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل قائمة البيانات");
                _dataList.Clear();
            }
        }

        /// <summary>
        /// إنشاء سجل جديد
        /// </summary>
        public virtual void CreateNewRecord()
        {
            _currentEntity = CreateNewEntityInstance();
            _currentIndex = -1;
            SetMode(FormMode.Add);
            LoadEntityToControls();
            UpdateNavigationInfo();
            UpdateButtonStates();
        }

        /// <summary>
        /// تعيين وضع النموذج
        /// </summary>
        /// <param name="mode">الوضع الجديد</param>
        public virtual void SetMode(FormMode mode)
        {
            CurrentMode = mode;
            SetControlsReadOnly(IsReadOnly);
            UpdateButtonStates();
            UpdateFormTitle();
        }

        /// <summary>
        /// تحديث عنوان النموذج حسب الوضع
        /// </summary>
        protected virtual void UpdateFormTitle()
        {
            string baseTitle = this.Text.Split('-')[0].Trim();
            string modeText = CurrentMode switch
            {
                FormMode.Add => "إضافة جديد",
                FormMode.Edit => "تعديل",
                FormMode.View => "عرض",
                _ => ""
            };

            if (!string.IsNullOrEmpty(modeText))
            {
                this.Text = $"{baseTitle} - {modeText}";
            }
        }

        #endregion

        #region Navigation Methods

        /// <summary>
        /// تحديث معلومات التنقل
        /// </summary>
        protected void UpdateNavigationInfo()
        {
            if (HasData)
            {
                int displayIndex = _currentIndex >= 0 ? _currentIndex + 1 : 0;
                barEditItem1.EditValue = $"{displayIndex} / {_dataList.Count}";

                barButtonItem4.Enabled = _currentIndex > 0; // First
                barButtonItem2.Enabled = _currentIndex > 0; // Previous
                barButtonItem3.Enabled = _currentIndex < _dataList.Count - 1; // Next
                barButtonItem1.Enabled = _currentIndex < _dataList.Count - 1; // Last
            }
            else
            {
                barEditItem1.EditValue = "0 / 0";
                barButtonItem4.Enabled = false;
                barButtonItem2.Enabled = false;
                barButtonItem3.Enabled = false;
                barButtonItem1.Enabled = false;
            }
        }

        /// <summary>
        /// الانتقال إلى سجل محدد
        /// </summary>
        /// <param name="index">فهرس السجل</param>
        protected void NavigateToRecord(int index)
        {
            if (index >= 0 && index < _dataList.Count)
            {
                _currentIndex = index;
                _currentEntity = _dataList[_currentIndex];
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// تحديث حالة الأزرار
        /// </summary>
        protected void UpdateButtonStates()
        {
            barButtonItem_Save.Enabled = !IsReadOnly;
            barButtonItem_Delete.Enabled = _currentEntity != null && CurrentMode != FormMode.Add;
            barButtonItem_Print.Enabled = _currentEntity != null && CurrentMode != FormMode.Add;

            // تحديث نص زر الحفظ حسب الوضع
            UpdateSaveButtonCaption();
        }

        /// <summary>
        /// تحديث نص زر الحفظ حسب الوضع الحالي
        /// </summary>
        protected virtual void UpdateSaveButtonCaption()
        {
            barButtonItem_Save.Caption = CurrentMode switch
            {
                FormMode.Add => "💾 حفظ",
                FormMode.Edit => "💾 تعديل",
                FormMode.View => "💾 حفظ",
                _ => "💾 حفظ"
            };
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// معالج حدث زر جديد
        /// </summary>
        protected virtual void BarButtonItem_New_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            CreateNewRecord();
        }

        /// <summary>
        /// معالج حدث زر حفظ
        /// </summary>
        protected virtual async void BarButtonItem_Save_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            await SaveCurrentEntityAsync();
        }

        /// <summary>
        /// معالج حدث زر حذف
        /// </summary>
        protected virtual async void BarButtonItem_Delete_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            await DeleteCurrentEntityAsync();
        }

        /// <summary>
        /// معالج حدث زر طباعة
        /// </summary>
        protected virtual void BarButtonItem_Print_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            PrintCurrentEntity();
        }

        /// <summary>
        /// معالج حدث زر الأول
        /// </summary>
        protected virtual void BarButtonItem4_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            NavigateToRecord(0);
        }

        /// <summary>
        /// معالج حدث زر السابق
        /// </summary>
        protected virtual void BarButtonItem2_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            NavigateToRecord(_currentIndex - 1);
        }

        /// <summary>
        /// معالج حدث زر التالي
        /// </summary>
        protected virtual void BarButtonItem3_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            NavigateToRecord(_currentIndex + 1);
        }

        /// <summary>
        /// معالج حدث زر الأخير
        /// </summary>
        protected virtual void BarButtonItem1_ItemClick(object? sender, DevExpress.XtraBars.ItemClickEventArgs? e)
        {
            if (HasData)
                NavigateToRecord(_dataList.Count - 1);
        }

        /// <summary>
        /// معالج حدث تغيير رقم السجل
        /// </summary>
        private void BarEditItem1_EditValueChanged(object? sender, EventArgs e)
        {
            if (int.TryParse(barEditItem1.EditValue?.ToString(), out int recordNumber))
            {
                if (recordNumber > 0 && recordNumber <= _dataList.Count)
                {
                    NavigateToRecord(recordNumber - 1);
                }
            }
        }

        /// <summary>
        /// معالج أحداث لوحة المفاتيح
        /// </summary>
        protected virtual void OnFormKeyDown(object? sender, KeyEventArgs e)
        {
            if (e.Control)
            {
                switch (e.KeyCode)
                {
                    case Keys.N:
                        BarButtonItem_New_ItemClick(null, null);
                        e.Handled = true;
                        break;
                    case Keys.S:
                        BarButtonItem_Save_ItemClick(null, null);
                        e.Handled = true;
                        break;
                    case Keys.P:
                        BarButtonItem_Print_ItemClick(null, null);
                        e.Handled = true;
                        break;
                }
            }
            else
            {
                switch (e.KeyCode)
                {
                    case Keys.Delete:
                        if (_currentEntity != null && CurrentMode != FormMode.Add)
                        {
                            BarButtonItem_Delete_ItemClick(null, null);
                            e.Handled = true;
                        }
                        break;
                    case Keys.F5:
                        _ = LoadDataListAsync();
                        e.Handled = true;
                        break;
                    case Keys.Home:
                        BarButtonItem4_ItemClick(null, null);
                        e.Handled = true;
                        break;
                    case Keys.End:
                        BarButtonItem1_ItemClick(null, null);
                        e.Handled = true;
                        break;
                    case Keys.PageUp:
                        BarButtonItem2_ItemClick(null, null);
                        e.Handled = true;
                        break;
                    case Keys.PageDown:
                        BarButtonItem3_ItemClick(null, null);
                        e.Handled = true;
                        break;
                }
            }
        }

        #endregion

        #region CRUD Operations

        /// <summary>
        /// حفظ الكائن الحالي
        /// </summary>
        public virtual async Task<bool> SaveCurrentEntityAsync()
        {
            if (!ValidateEntityData())
                return false;

            try
            {
                LoadControlsToEntity();

                bool isEdit = CurrentMode == FormMode.Edit;
                bool success = await SaveEntityToDatabase(_currentEntity!);

                if (success)
                {
                    string message = GetSaveSuccessMessage(isEdit);
                    XtraMessageBox.Show(message, "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تعيين النتيجة للنافذة
                    this.DialogResult = DialogResult.OK;

                    return true;
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "حفظ البيانات");
            }

            return false;
        }

        /// <summary>
        /// حذف الكائن الحالي
        /// </summary>
        public virtual async Task<bool> DeleteCurrentEntityAsync()
        {
            if (_currentEntity == null || CurrentMode == FormMode.Add)
                return false;

            string confirmMessage = GetDeleteConfirmationMessage();
            if (XtraMessageBox.Show(confirmMessage, "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) != DialogResult.Yes)
                return false;

            try
            {
                bool success = await DeleteEntityFromDatabase(_currentEntity);

                if (success)
                {
                    XtraMessageBox.Show("تم حذف البيانات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تعيين النتيجة للنافذة
                    this.DialogResult = DialogResult.OK;

                    return true;
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف البيانات");
            }

            return false;
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }

}