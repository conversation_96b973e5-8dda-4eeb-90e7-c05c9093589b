namespace WinFormsApp1.Forms
{
    partial class VehiclesForm
    {
        private System.ComponentModel.IContainer components = null;

        private void InitializeComponent()
        {
            gridControl1 = new DevExpress.XtraGrid.GridControl();
            gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            colVehicleId = new DevExpress.XtraGrid.Columns.GridColumn();
            colVehicleName = new DevExpress.XtraGrid.Columns.GridColumn();
            colTransportCompanyName = new DevExpress.XtraGrid.Columns.GridColumn();
            colVehicleTypeName = new DevExpress.XtraGrid.Columns.GridColumn();
            colDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            colCreatedAt = new DevExpress.XtraGrid.Columns.GridColumn();
            panelControl1 = new DevExpress.XtraEditors.PanelControl();
            panelControl2 = new DevExpress.XtraEditors.PanelControl();
            btnRefresh = new DevExpress.XtraEditors.SimpleButton();
            btnExportToExcel = new DevExpress.XtraEditors.SimpleButton();
            btnPrint = new DevExpress.XtraEditors.SimpleButton();
            btnView = new DevExpress.XtraEditors.SimpleButton();
            btnNew = new DevExpress.XtraEditors.SimpleButton();
            labelControl1 = new DevExpress.XtraEditors.LabelControl();
            panelControl3 = new DevExpress.XtraEditors.PanelControl();
            txtSearch = new DevExpress.XtraEditors.TextEdit();
            lblSearch = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)gridControl1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).BeginInit();
            panelControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl2).BeginInit();
            panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl3).BeginInit();
            panelControl3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).BeginInit();
            SuspendLayout();

            gridControl1.Dock = DockStyle.Fill;
            gridControl1.Location = new Point(0, 108);
            gridControl1.MainView = gridView1;
            gridControl1.Name = "gridControl1";
            gridControl1.Size = new Size(1200, 492);
            gridControl1.TabIndex = 0;
            gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] { gridView1 });

            gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] { colVehicleId, colVehicleName, colTransportCompanyName, colVehicleTypeName, colDescription, colCreatedAt });
            gridView1.GridControl = gridControl1;
            gridView1.Name = "gridView1";
            gridView1.OptionsBehavior.ReadOnly = true;
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsSelection.MultiSelect = false;
            gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;

            colVehicleId.Caption = "المعرف";
            colVehicleId.FieldName = "VehicleId";
            colVehicleId.Name = "colVehicleId";
            colVehicleId.Visible = true;
            colVehicleId.VisibleIndex = 0;
            colVehicleId.Width = 80;

            colVehicleName.Caption = "اسم آلية النقل";
            colVehicleName.FieldName = "VehicleName";
            colVehicleName.Name = "colVehicleName";
            colVehicleName.Visible = true;
            colVehicleName.VisibleIndex = 1;
            colVehicleName.Width = 250;

            colTransportCompanyName.Caption = "شركة النقل";
            colTransportCompanyName.FieldName = "TransportCompany.Name";
            colTransportCompanyName.Name = "colTransportCompanyName";
            colTransportCompanyName.Visible = true;
            colTransportCompanyName.VisibleIndex = 2;
            colTransportCompanyName.Width = 200;

            colVehicleTypeName.Caption = "نوع الآلية";
            colVehicleTypeName.FieldName = "VehicleType.VehicleTypeName";
            colVehicleTypeName.Name = "colVehicleTypeName";
            colVehicleTypeName.Visible = true;
            colVehicleTypeName.VisibleIndex = 3;
            colVehicleTypeName.Width = 150;

            colDescription.Caption = "الوصف";
            colDescription.FieldName = "Description";
            colDescription.Name = "colDescription";
            colDescription.Visible = true;
            colDescription.VisibleIndex = 4;
            colDescription.Width = 300;

            colCreatedAt.Caption = "تاريخ الإنشاء";
            colCreatedAt.FieldName = "CreatedAt";
            colCreatedAt.Name = "colCreatedAt";
            colCreatedAt.Visible = true;
            colCreatedAt.VisibleIndex = 5;
            colCreatedAt.Width = 120;

            panelControl1.Appearance.BackColor = Color.FromArgb(248, 249, 250);
            panelControl1.Appearance.Options.UseBackColor = true;
            panelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl1.Controls.Add(panelControl2);
            panelControl1.Controls.Add(labelControl1);
            panelControl1.Dock = DockStyle.Top;
            panelControl1.Location = new Point(0, 0);
            panelControl1.Name = "panelControl1";
            panelControl1.Size = new Size(1200, 68);
            panelControl1.TabIndex = 1;

            panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl2.Controls.Add(btnRefresh);
            panelControl2.Controls.Add(btnExportToExcel);
            panelControl2.Controls.Add(btnPrint);
            panelControl2.Controls.Add(btnView);
            panelControl2.Controls.Add(btnNew);
            panelControl2.Dock = DockStyle.Left;
            panelControl2.Location = new Point(0, 35);
            panelControl2.Name = "panelControl2";
            panelControl2.Size = new Size(600, 33);
            panelControl2.TabIndex = 1;

            btnRefresh.Appearance.BackColor = Color.FromArgb(52, 152, 219);
            btnRefresh.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Bold);
            btnRefresh.Appearance.ForeColor = Color.White;
            btnRefresh.Appearance.Options.UseBackColor = true;
            btnRefresh.Appearance.Options.UseFont = true;
            btnRefresh.Appearance.Options.UseForeColor = true;
            btnRefresh.Location = new Point(340, 5);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(75, 23);
            btnRefresh.TabIndex = 4;
            btnRefresh.Text = "🔄 تحديث";

            btnExportToExcel.Appearance.BackColor = Color.FromArgb(46, 204, 113);
            btnExportToExcel.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Bold);
            btnExportToExcel.Appearance.ForeColor = Color.White;
            btnExportToExcel.Appearance.Options.UseBackColor = true;
            btnExportToExcel.Appearance.Options.UseFont = true;
            btnExportToExcel.Appearance.Options.UseForeColor = true;
            btnExportToExcel.Location = new Point(255, 5);
            btnExportToExcel.Name = "btnExportToExcel";
            btnExportToExcel.Size = new Size(80, 23);
            btnExportToExcel.TabIndex = 3;
            btnExportToExcel.Text = "📊 Excel";

            btnPrint.Appearance.BackColor = Color.FromArgb(149, 165, 166);
            btnPrint.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Bold);
            btnPrint.Appearance.ForeColor = Color.White;
            btnPrint.Appearance.Options.UseBackColor = true;
            btnPrint.Appearance.Options.UseFont = true;
            btnPrint.Appearance.Options.UseForeColor = true;
            btnPrint.Location = new Point(175, 5);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(75, 23);
            btnPrint.TabIndex = 2;
            btnPrint.Text = "🖨️ طباعة";

            btnView.Appearance.BackColor = Color.FromArgb(241, 196, 15);
            btnView.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Bold);
            btnView.Appearance.ForeColor = Color.White;
            btnView.Appearance.Options.UseBackColor = true;
            btnView.Appearance.Options.UseFont = true;
            btnView.Appearance.Options.UseForeColor = true;
            btnView.Location = new Point(85, 5);
            btnView.Name = "btnView";
            btnView.Size = new Size(85, 23);
            btnView.TabIndex = 1;
            btnView.Text = "✏️ عرض/تعديل";

            btnNew.Appearance.BackColor = Color.FromArgb(52, 152, 219);
            btnNew.Appearance.Font = new Font("Tahoma", 8.25F, FontStyle.Bold);
            btnNew.Appearance.ForeColor = Color.White;
            btnNew.Appearance.Options.UseBackColor = true;
            btnNew.Appearance.Options.UseFont = true;
            btnNew.Appearance.Options.UseForeColor = true;
            btnNew.Location = new Point(5, 5);
            btnNew.Name = "btnNew";
            btnNew.Size = new Size(75, 23);
            btnNew.TabIndex = 0;
            btnNew.Text = "🆕 جديد";

            labelControl1.Appearance.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            labelControl1.Appearance.ForeColor = Color.FromArgb(52, 73, 94);
            labelControl1.Appearance.Options.UseFont = true;
            labelControl1.Appearance.Options.UseForeColor = true;
            labelControl1.Dock = DockStyle.Top;
            labelControl1.Location = new Point(0, 0);
            labelControl1.Name = "labelControl1";
            labelControl1.Padding = new Padding(0, 12, 0, 8);
            labelControl1.Size = new Size(1200, 35);
            labelControl1.TabIndex = 0;
            labelControl1.Text = "🚛 إدارة آليات النقل - إجمالي الآليات: 0";

            panelControl3.Appearance.BackColor = Color.FromArgb(236, 240, 241);
            panelControl3.Appearance.Options.UseBackColor = true;
            panelControl3.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            panelControl3.Controls.Add(txtSearch);
            panelControl3.Controls.Add(lblSearch);
            panelControl3.Dock = DockStyle.Top;
            panelControl3.Location = new Point(0, 68);
            panelControl3.Name = "panelControl3";
            panelControl3.Size = new Size(1200, 40);
            panelControl3.TabIndex = 2;

            txtSearch.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            txtSearch.Location = new Point(12, 8);
            txtSearch.Name = "txtSearch";
            txtSearch.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtSearch.Properties.Appearance.Options.UseFont = true;
            txtSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            txtSearch.Properties.NullText = "🔍 البحث في آليات النقل (اسم الآلية، شركة النقل، نوع الآلية)...";
            txtSearch.Properties.NullValuePrompt = "🔍 البحث في آليات النقل...";
            txtSearch.Size = new Size(1100, 24);
            txtSearch.TabIndex = 1;

            lblSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblSearch.Appearance.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            lblSearch.Appearance.ForeColor = Color.FromArgb(52, 73, 94);
            lblSearch.Appearance.Options.UseFont = true;
            lblSearch.Appearance.Options.UseForeColor = true;
            lblSearch.Location = new Point(1118, 10);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(76, 16);
            lblSearch.TabIndex = 0;
            lblSearch.Text = "🔍 البحث:";

            AutoScaleDimensions = new SizeF(6F, 13F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1200, 600);
            Controls.Add(gridControl1);
            Controls.Add(panelControl3);
            Controls.Add(panelControl1);
            Name = "VehiclesForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "🚛 إدارة آليات النقل";
            ((System.ComponentModel.ISupportInitialize)gridControl1).EndInit();
            ((System.ComponentModel.ISupportInitialize)gridView1).EndInit();
            ((System.ComponentModel.ISupportInitialize)panelControl1).EndInit();
            panelControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)panelControl2).EndInit();
            panelControl2.ResumeLayout(false);
            panelControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)panelControl3).EndInit();
            panelControl3.ResumeLayout(false);
            panelControl3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)txtSearch.Properties).EndInit();
            ResumeLayout(false);
        }

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleId;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleName;
        private DevExpress.XtraGrid.Columns.GridColumn colTransportCompanyName;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleTypeName;
        private DevExpress.XtraGrid.Columns.GridColumn colDescription;
        private DevExpress.XtraGrid.Columns.GridColumn colCreatedAt;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraEditors.SimpleButton btnRefresh;
        private DevExpress.XtraEditors.SimpleButton btnExportToExcel;
        private DevExpress.XtraEditors.SimpleButton btnPrint;
        private DevExpress.XtraEditors.SimpleButton btnView;
        private DevExpress.XtraEditors.SimpleButton btnNew;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.PanelControl panelControl3;
        private DevExpress.XtraEditors.TextEdit txtSearch;
        private DevExpress.XtraEditors.LabelControl lblSearch;
    }
}
