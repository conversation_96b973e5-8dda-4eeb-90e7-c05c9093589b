using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using System;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج اختبار بسيط لشركات النقل
    /// </summary>
    public partial class TestTransportCompanyForm : XtraForm
    {
        private WarehouseDbContext _context;
        private TextEdit txtName;
        private MemoEdit memoContactDetails;
        private SimpleButton btnSave;
        private SimpleButton btnCancel;

        public TestTransportCompanyForm()
        {
            InitializeComponent();
            _context = new WarehouseDbContext();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "اختبار شركة النقل";
            this.Size = new System.Drawing.Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إنشاء عناصر التحكم
            var lblName = new LabelControl();
            lblName.Text = "اسم الشركة:";
            lblName.Location = new System.Drawing.Point(20, 20);
            lblName.Size = new System.Drawing.Size(100, 20);

            txtName = new TextEdit();
            txtName.Location = new System.Drawing.Point(130, 20);
            txtName.Size = new System.Drawing.Size(200, 20);

            var lblContact = new LabelControl();
            lblContact.Text = "تفاصيل الاتصال:";
            lblContact.Location = new System.Drawing.Point(20, 60);
            lblContact.Size = new System.Drawing.Size(100, 20);

            memoContactDetails = new MemoEdit();
            memoContactDetails.Location = new System.Drawing.Point(130, 60);
            memoContactDetails.Size = new System.Drawing.Size(200, 80);

            btnSave = new SimpleButton();
            btnSave.Text = "حفظ";
            btnSave.Location = new System.Drawing.Point(130, 160);
            btnSave.Size = new System.Drawing.Size(80, 30);
            btnSave.Click += BtnSave_Click;

            btnCancel = new SimpleButton();
            btnCancel.Text = "إلغاء";
            btnCancel.Location = new System.Drawing.Point(220, 160);
            btnCancel.Size = new System.Drawing.Size(80, 30);
            btnCancel.Click += (s, e) => this.Close();

            // إضافة عناصر التحكم للنموذج
            this.Controls.Add(lblName);
            this.Controls.Add(txtName);
            this.Controls.Add(lblContact);
            this.Controls.Add(memoContactDetails);
            this.Controls.Add(btnSave);
            this.Controls.Add(btnCancel);
        }

        private async void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الشركة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var transportCompany = new TransportCompany
                {
                    Name = txtName.Text.Trim(),
                    ContactDetails = string.IsNullOrWhiteSpace(memoContactDetails.Text) ? null : memoContactDetails.Text.Trim(),
                    CreatedAt = DateTime.Now,
                    CreatedByUserId = 1
                };

                _context.TransportCompanies.Add(transportCompany);
                int result = await _context.SaveChangesAsync();

                if (result > 0)
                {
                    MessageBox.Show($"تم حفظ شركة النقل بنجاح. ID: {transportCompany.TransportCompanyId}", "نجح الحفظ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("لم يتم حفظ أي سجل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في حفظ شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل الخطأ:\n{ex.InnerException.Message}";
                }
                MessageBox.Show(errorMessage, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
