using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل شركات النقل
    /// </summary>
    public partial class TransportCompanyAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة شركة نقل جديدة
        /// </summary>
        public TransportCompanyAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = new TransportCompany();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل شركة نقل موجودة
        /// </summary>
        public TransportCompanyAddEditForm(TransportCompany selectedTransportCompany)
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = selectedTransportCompany;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var transportCompanies = await _context!.TransportCompanies
                    .Include(tc => tc.CreatedByUser)
                    .Include(tc => tc.ModifiedByUser)
                    .OrderBy(tc => tc.Name)
                    .ToListAsync();

                return transportCompanies.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل شركات النقل من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new TransportCompany
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                txtName.Text = transportCompany.Name ?? "";
                memoContactDetails.Text = transportCompany.ContactDetails ?? "";
                
                // عرض معلومات الإنشاء والتعديل
                if (transportCompany.TransportCompanyId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {transportCompany.CreatedAt:dd/MM/yyyy HH:mm}";
                    if (transportCompany.ModifiedAt.HasValue)
                    {
                        lblModifiedAt.Text = $"تاريخ التعديل: {transportCompany.ModifiedAt:dd/MM/yyyy HH:mm}";
                        lblModifiedAt.Visible = true;
                    }
                    else
                    {
                        lblModifiedAt.Visible = false;
                    }
                }
                else
                {
                    lblCreatedAt.Text = "";
                    lblModifiedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل البيانات من عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                // تسجيل معلومات التشخيص
                System.Diagnostics.Debug.WriteLine($"تحميل البيانات من عناصر التحكم - ID: {transportCompany.TransportCompanyId}");
                System.Diagnostics.Debug.WriteLine($"اسم الشركة من النموذج: '{txtName.Text}'");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الاتصال من النموذج: '{memoContactDetails.Text}'");

                // تحديث البيانات الأساسية
                transportCompany.Name = string.IsNullOrWhiteSpace(txtName.Text) ? null : txtName.Text.Trim();
                transportCompany.ContactDetails = string.IsNullOrWhiteSpace(memoContactDetails.Text) ? null : memoContactDetails.Text.Trim();

                // تسجيل البيانات بعد التحديث
                System.Diagnostics.Debug.WriteLine($"اسم الشركة بعد التحديث: '{transportCompany.Name}'");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الاتصال بعد التحديث: '{transportCompany.ContactDetails}'");

                // ملاحظة: تواريخ الإنشاء والتعديل يتم تعيينها في SaveEntityToDatabase
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("_currentEntity ليس من نوع TransportCompany");
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء التحقق من صحة البيانات");

                // التحقق من اسم شركة النقل
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    System.Diagnostics.Debug.WriteLine("خطأ: اسم شركة النقل فارغ");
                    XtraMessageBox.Show("يرجى إدخال اسم شركة النقل", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول اسم شركة النقل
                if (txtName.Text.Trim().Length > 100)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ: اسم شركة النقل طويل جداً ({txtName.Text.Trim().Length} حرف)");
                    XtraMessageBox.Show("اسم شركة النقل يجب أن يكون أقل من 100 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول تفاصيل الاتصال
                if (!string.IsNullOrWhiteSpace(memoContactDetails.Text) && memoContactDetails.Text.Trim().Length > 500)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ: تفاصيل الاتصال طويلة جداً ({memoContactDetails.Text.Trim().Length} حرف)");
                    XtraMessageBox.Show("تفاصيل الاتصال يجب أن تكون أقل من 500 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    memoContactDetails.Focus();
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("التحقق من صحة البيانات نجح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من صحة البيانات: {ex.Message}");
                HandleError(ex, "التحقق من صحة البيانات");
                return false;
            }
        }

        /// <summary>
        /// حفظ شركة النقل في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is TransportCompany transportCompany)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        transportCompany.CreatedAt = DateTime.Now;
                        transportCompany.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.TransportCompanies.Add(transportCompany);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingTransportCompany = await _context!.TransportCompanies.FindAsync(transportCompany.TransportCompanyId);
                        if (existingTransportCompany != null)
                        {
                            existingTransportCompany.Name = transportCompany.Name;
                            existingTransportCompany.ContactDetails = transportCompany.ContactDetails;
                            existingTransportCompany.ModifiedAt = DateTime.Now;
                            existingTransportCompany.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.TransportCompanies.Update(existingTransportCompany);
                        }
                        else
                        {
                            return false; // شركة النقل غير موجودة
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ شركة النقل");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف شركة النقل من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is TransportCompany transportCompany)
            {
                try
                {
                    // البحث عن شركة النقل في قاعدة البيانات للتأكد من وجودها
                    var existingTransportCompany = await _context!.TransportCompanies.FindAsync(transportCompany.TransportCompanyId);
                    if (existingTransportCompany != null)
                    {
                        _context.TransportCompanies.Remove(existingTransportCompany);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("شركة النقل غير موجودة"), "حذف شركة النقل");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف شركة النقل");
                    return false;
                }
            }
            return false;
        }

        #endregion



        #region Error Handling

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        protected override void HandleError(Exception ex, string operation)
        {
            string message = $"خطأ في {operation}:\n{ex.Message}";
            XtraMessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Virtual Methods Override

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                return $"هل أنت متأكد من حذف شركة النقل '{transportCompany.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف شركة النقل؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                return isEdit ?
                    $"تم تحديث شركة النقل '{transportCompany.Name}' بنجاح" :
                    $"تم إنشاء شركة النقل '{transportCompany.Name}' بنجاح";
            }
            return isEdit ? "تم تحديث شركة النقل بنجاح" : "تم إنشاء شركة النقل بنجاح";
        }

        /// <summary>
        /// تحميل قائمة البيانات للتنقل
        /// </summary>
        public override async Task LoadDataListAsync()
        {
            try
            {
                _dataList = await _context!.TransportCompanies
                    .Include(tc => tc.CreatedByUser)
                    .Include(tc => tc.ModifiedByUser)
                    .OrderBy(tc => tc.Name)
                    .Cast<object>()
                    .ToListAsync();

                // تحديد الموقع الحالي إذا كان في وضع التعديل
                if (CurrentMode == FormMode.Edit && _currentEntity is TransportCompany currentTransportCompany)
                {
                    _currentIndex = _dataList.FindIndex(tc => ((TransportCompany)tc).TransportCompanyId == currentTransportCompany.TransportCompanyId);
                    if (_currentIndex < 0) _currentIndex = 0;
                }
                else
                {
                    _currentIndex = 0;
                }

                UpdateNavigationInfo();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل قائمة شركات النقل");
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
