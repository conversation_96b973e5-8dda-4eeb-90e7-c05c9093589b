using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل المتعاملين
    /// </summary>
    public partial class PartyAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة متعامل جديد
        /// </summary>
        public PartyAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "👥 إدارة المتعاملين";

                _currentEntity = new Party();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة المتعامل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل متعامل موجود
        /// </summary>
        public PartyAddEditForm(Party selectedParty)
        {
            try
            {
                InitializeComponent();
                this.Text = "👥 إدارة المتعاملين";

                _currentEntity = selectedParty;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل المتعامل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var parties = await _context!.Parties
                    .Include(p => p.CreatedByUser)
                    .Include(p => p.ModifiedByUser)
                    .OrderBy(p => p.Name)
                    .ToListAsync();

                return parties.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل المتعاملين من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Party
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Party party)
            {
                txtName.Text = party.Name ?? "";
                txtContactName.Text = party.ContactName ?? "";
                txtPhone.Text = party.Phone ?? "";
                txtEmail.Text = party.Email ?? "";
                memoAddress.Text = party.Address ?? "";
                
                // عرض تاريخ الإنشاء
                if (party.PartyId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {party.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Party party)
            {
                party.Name = txtName.Text.Trim();
                party.ContactName = string.IsNullOrWhiteSpace(txtContactName.Text) ? null : txtContactName.Text.Trim();
                party.Phone = string.IsNullOrWhiteSpace(txtPhone.Text) ? null : txtPhone.Text.Trim();
                party.Email = string.IsNullOrWhiteSpace(txtEmail.Text) ? null : txtEmail.Text.Trim();
                party.Address = string.IsNullOrWhiteSpace(memoAddress.Text) ? null : memoAddress.Text.Trim();
                
                // تعيين تاريخ الإنشاء للمتعاملين الجدد
                if (party.PartyId == 0)
                {
                    party.CreatedAt = DateTime.Now;
                }
                else
                {
                    party.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم المتعامل
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم المتعامل", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول اسم المتعامل
            if (txtName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم المتعامل لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول اسم جهة الاتصال
            if (!string.IsNullOrWhiteSpace(txtContactName.Text) && txtContactName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم جهة الاتصال لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtContactName.Focus();
                return false;
            }

            // التحقق من طول رقم الهاتف
            if (!string.IsNullOrWhiteSpace(txtPhone.Text) && txtPhone.Text.Trim().Length > 50)
            {
                XtraMessageBox.Show("رقم الهاتف لا يجب أن يتجاوز 50 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            // التحقق من طول البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && txtEmail.Text.Trim().Length > 100)
            {
                XtraMessageBox.Show("البريد الإلكتروني لا يجب أن يتجاوز 100 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmail.Focus();
                return false;
            }

            // التحقق من صحة البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                var email = txtEmail.Text.Trim();
                if (!IsValidEmail(email))
                {
                    XtraMessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            // التحقق من طول العنوان
            if (!string.IsNullOrWhiteSpace(memoAddress.Text) && memoAddress.Text.Trim().Length > 300)
            {
                XtraMessageBox.Show("العنوان لا يجب أن يتجاوز 300 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                memoAddress.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم المتعامل
            if (_currentEntity is Party currentParty)
            {
                var existingParty = _context!.Parties.FirstOrDefault(p => 
                    p.Name == txtName.Text.Trim() && p.PartyId != currentParty.PartyId);
                
                if (existingParty != null)
                {
                    XtraMessageBox.Show("اسم المتعامل موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة البريد الإلكتروني
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// حفظ المتعامل في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Party party)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        party.CreatedAt = DateTime.Now;
                        party.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Parties.Add(party);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingParty = await _context!.Parties.FindAsync(party.PartyId);
                        if (existingParty != null)
                        {
                            existingParty.Name = party.Name;
                            existingParty.ContactName = party.ContactName;
                            existingParty.Phone = party.Phone;
                            existingParty.Email = party.Email;
                            existingParty.Address = party.Address;
                            existingParty.ModifiedAt = DateTime.Now;
                            existingParty.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Parties.Update(existingParty);
                        }
                        else
                        {
                            return false; // المتعامل غير موجود
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ المتعامل");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف المتعامل من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Party party)
            {
                try
                {
                    // البحث عن المتعامل في قاعدة البيانات للتأكد من وجوده
                    var existingParty = await _context!.Parties.FindAsync(party.PartyId);
                    if (existingParty != null)
                    {
                        _context.Parties.Remove(existingParty);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("المتعامل غير موجود"), "حذف المتعامل");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف المتعامل");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Party party)
            {
                return $"هل أنت متأكد من حذف المتعامل '{party.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذا المتعامل؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث بيانات المتعامل بنجاح" : "تم إضافة المتعامل الجديد بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Party party)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "👥 إضافة متعامل جديد",
                    FormMode.Edit => $"👥 تعديل المتعامل: {party.Name}",
                    FormMode.View => $"👥 عرض المتعامل: {party.Name}",
                    _ => "👥 إدارة المتعاملين"
                };
            }
            else
            {
                this.Text = "👥 إدارة المتعاملين";
            }
        }

        #endregion
    }
}
