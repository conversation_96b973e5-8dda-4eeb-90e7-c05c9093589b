using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إدارة آليات النقل
    /// </summary>
    public partial class VehiclesForm : XtraForm
    {
        #region Fields

        private readonly WarehouseDbContext _context;
        private bool _isFormOpening = false;

        #endregion

        #region Constructor

        public VehiclesForm()
        {
            InitializeComponent();
            _context = new WarehouseDbContext();
            
            // تهيئة الأحداث
            this.Load += VehiclesForm_Load;
            this.KeyDown += VehiclesForm_KeyDown;
            this.Resize += VehiclesForm_Resize;
            
            // تهيئة أحداث الأزرار
            btnNew.Click += btnNew_Click;
            btnView.Click += btnView_Click;
            btnRefresh.Click += btnRefresh_Click;
            btnPrint.Click += btnPrint_Click;
            btnExportToExcel.Click += btnExportToExcel_Click;
            
            // تهيئة أحداث البحث والشبكة
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;
            
            // تهيئة أحداث الشبكة
            GridView1.DoubleClick += GridView1_DoubleClick;
            GridView1.KeyDown += GridView1_KeyDown;
            GridView1.DataSourceChanged += GridView1_DataSourceChanged;
        }

        #endregion

        #region Form Events

        private async void VehiclesForm_Load(object sender, EventArgs e)
        {
            try
            {
                _isFormOpening = true;
                await LoadDataAsync();
                SetupGridColumns();
                _isFormOpening = false;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void VehiclesForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F5) btnRefresh_Click(sender, e);
            else if (e.KeyCode == Keys.F2) btnNew_Click(sender, e);
            else if (e.KeyCode == Keys.Enter) btnView_Click(sender, e);
        }

        private void VehiclesForm_Resize(object sender, EventArgs e)
        {
            // تعديل حجم الأعمدة عند تغيير حجم النموذج
        }

        #endregion

        #region Button Events

        private async void btnNew_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء وفتح نموذج الإضافة
                using (var addForm = new VehicleAddEditForm())
                {
                    var result = addForm.ShowDialog(this);
                    if (result == DialogResult.OK)
                    {
                        await LoadDataAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في فتح نموذج الإضافة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnView_Click(object sender, EventArgs e)
        {
            try
            {
                var selectedVehicle = GetSelectedVehicle();
                if (selectedVehicle != null)
                {
                    using (var editForm = new VehicleAddEditForm(selectedVehicle))
                    {
                        var result = editForm.ShowDialog(this);
                        if (result == DialogResult.OK)
                        {
                            await LoadDataAsync();
                        }
                    }
                }
                else
                {
                    XtraMessageBox.Show("يرجى اختيار آلية نقل للعرض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في فتح نموذج العرض: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                await LoadDataAsync();
                XtraMessageBox.Show("تم تحديث البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                GridControl1.ShowPrintPreview();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx";
                    saveDialog.Title = "حفظ ملف Excel";
                    saveDialog.FileName = $"آليات_النقل_{DateTime.Now:yyyy-MM-dd}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        GridView1.ExportToXlsx(saveDialog.FileName);
                        XtraMessageBox.Show("تم تصدير البيانات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Search Events

        private async void txtSearch_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_isFormOpening)
                {
                    await LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء البحث المؤقتة
            }
        }

        #endregion

        #region Grid Events

        private async void GridView1_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, new EventArgs());
        }

        private async void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnView_Click(sender, new EventArgs());
            }
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            // تحديث عدد السجلات أو أي معلومات أخرى
        }

        #endregion

        #region Data Methods

        /// <summary>
        /// تحميل البيانات من قاعدة البيانات
        /// </summary>
        private async Task LoadDataAsync()
        {
            try
            {
                var query = _context.Vehicles
                    .Include(v => v.TransportCompany)
                    .Include(v => v.VehicleType)
                    .Include(v => v.CreatedByUser)
                    .Include(v => v.ModifiedByUser)
                    .AsQueryable();

                // تطبيق البحث إذا كان هناك نص بحث
                var searchText = txtSearch.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    query = query.Where(v => 
                        v.VehicleName.Contains(searchText) ||
                        v.TransportCompany.Name.Contains(searchText) ||
                        v.VehicleType.VehicleTypeName.Contains(searchText) ||
                        v.Description.Contains(searchText));
                }

                var vehicles = await query
                    .OrderBy(v => v.VehicleName)
                    .ToListAsync();

                GridControl1.DataSource = vehicles;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحميل آليات النقل: {ex.Message}");
            }
        }

        /// <summary>
        /// إعداد أعمدة الشبكة
        /// </summary>
        private void SetupGridColumns()
        {
            try
            {
                GridView1.Columns.Clear();

                // إخفاء الأعمدة غير المرغوب فيها
                GridView1.OptionsView.ShowAutoFilterRow = true;
                GridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
                GridView1.OptionsSelection.MultiSelect = false;

                // إعداد الأعمدة المرئية
                var colVehicleName = GridView1.Columns.AddField("VehicleName");
                colVehicleName.Caption = "اسم الآلية";
                colVehicleName.Visible = true;
                colVehicleName.Width = 150;

                var colTransportCompany = GridView1.Columns.AddField("TransportCompany.Name");
                colTransportCompany.Caption = "شركة النقل";
                colTransportCompany.Visible = true;
                colTransportCompany.Width = 150;

                var colVehicleType = GridView1.Columns.AddField("VehicleType.VehicleTypeName");
                colVehicleType.Caption = "نوع الآلية";
                colVehicleType.Visible = true;
                colVehicleType.Width = 120;

                var colDescription = GridView1.Columns.AddField("Description");
                colDescription.Caption = "الوصف";
                colDescription.Visible = true;
                colDescription.Width = 200;

                var colCreatedAt = GridView1.Columns.AddField("CreatedAt");
                colCreatedAt.Caption = "تاريخ الإنشاء";
                colCreatedAt.Visible = true;
                colCreatedAt.Width = 120;
                colCreatedAt.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreatedAt.DisplayFormat.FormatString = "dd/MM/yyyy";

                var colCreatedBy = GridView1.Columns.AddField("CreatedByUser.Username");
                colCreatedBy.Caption = "أنشئ بواسطة";
                colCreatedBy.Visible = true;
                colCreatedBy.Width = 100;

                // إخفاء باقي الأعمدة
                foreach (DevExpress.XtraGrid.Columns.GridColumn col in GridView1.Columns)
                {
                    if (!col.Visible)
                    {
                        col.OptionsColumn.ShowInCustomizationForm = false;
                    }
                }

                GridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء إعداد الأعمدة
            }
        }

        /// <summary>
        /// الحصول على آلية النقل المحددة
        /// </summary>
        private Vehicle GetSelectedVehicle()
        {
            try
            {
                var selectedRowHandle = GridView1.FocusedRowHandle;
                if (selectedRowHandle >= 0)
                {
                    return GridView1.GetRow(selectedRowHandle) as Vehicle;
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
