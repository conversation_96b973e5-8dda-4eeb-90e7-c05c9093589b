using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// فورم عرض جدول شركات النقل مع عمليات CRUD
    /// </summary>
    public partial class TransportCompaniesForm : XtraForm
    {
        #region Fields

        private readonly WarehouseDbContext _context;
        private bool _isFormOpening = false; // لمنع فتح النافذة أكثر من مرة
        private List<TransportCompany> _allTransportCompanies = new List<TransportCompany>(); // قائمة جميع شركات النقل للبحث

        #endregion

        #region Constructor

        public TransportCompaniesForm()
        {
            try
            {
                InitializeComponent();
                _context = DatabaseService.GetDbContext();
                SetupForm();
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج شركات النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                message += $"\n\nStack Trace:\n{ex.StackTrace}";
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Setup Methods

        /// <summary>
        /// إعداد النموذج والأحداث
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // إعداد النموذج
                this.Text = "🚛 إدارة شركات النقل";
                this.WindowState = FormWindowState.Maximized;
                this.RightToLeft = RightToLeft.Yes;
                this.RightToLeftLayout = true;

                // إعداد الشبكة
                SetupGrid();

                // إعداد الأحداث
                SetupEvents();

                // إعداد أزرار شريط الأدوات
                SetupToolbarButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الشبكة
        /// </summary>
        private void SetupGrid()
        {
            try
            {
                // إعداد الشبكة الرئيسية
                gridControl1.RightToLeft = RightToLeft.Yes;
                
                // إعداد الأعمدة
                gridView1.Columns.Clear();
                
                // عمود المعرف
                var colId = gridView1.Columns.AddField("TransportCompanyId");
                colId.Caption = "المعرف";
                colId.Width = 80;
                colId.Visible = true;
                colId.Fixed = DevExpress.XtraGrid.Columns.FixedStyle.Left;

                // عمود الاسم
                var colName = gridView1.Columns.AddField("Name");
                colName.Caption = "اسم شركة النقل";
                colName.Width = 350;
                colName.Visible = true;

                // عمود تفاصيل الاتصال
                var colContactDetails = gridView1.Columns.AddField("ContactDetails");
                colContactDetails.Caption = "تفاصيل الاتصال";
                colContactDetails.Width = 500;
                colContactDetails.Visible = true;

                // عمود تاريخ الإنشاء
                var colCreatedAt = gridView1.Columns.AddField("CreatedAt");
                colCreatedAt.Caption = "تاريخ الإنشاء";
                colCreatedAt.Width = 200;
                colCreatedAt.Visible = true;
                colCreatedAt.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                colCreatedAt.DisplayFormat.FormatString = "dd/MM/yyyy HH:mm";

                // إعدادات عامة للشبكة
                gridView1.OptionsView.ShowGroupPanel = false;
                gridView1.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.Never;
                gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
                gridView1.OptionsSelection.MultiSelect = false;
                gridView1.OptionsView.ShowIndicator = true;
                gridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True;
                gridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True;
                gridView1.OptionsView.RowAutoHeight = true;
                gridView1.OptionsView.ColumnAutoWidth = true; // تمكين العرض التلقائي
                gridView1.BestFitColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الشبكة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            // أحداث النموذج
            this.Load += TransportCompaniesForm_Load;
            this.KeyPreview = true;
            this.KeyDown += TransportCompaniesForm_KeyDown;
            this.Resize += TransportCompaniesForm_Resize;

            // أحداث الأزرار
            btnNew.Click += btnNew_Click;
            btnView.Click += btnView_Click;
            btnRefresh.Click += btnRefresh_Click;
            btnPrint.Click += btnPrint_Click;
            btnExportToExcel.Click += btnExportToExcel_Click;

            // أحداث البحث
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;

            // أحداث الشبكة
            gridView1.DoubleClick += GridView1_DoubleClick;
            gridView1.KeyDown += GridView1_KeyDown;
            gridView1.DataSourceChanged += GridView1_DataSourceChanged;
        }

        /// <summary>
        /// إعداد أزرار شريط الأدوات
        /// </summary>
        private void SetupToolbarButtons()
        {
            try
            {
                // زر جديد
                btnNew.Text = "🆕 جديد";
                btnNew.ToolTip = "إضافة شركة نقل جديدة (Ctrl+N)";

                // زر عرض/تعديل
                btnView.Text = "✏️ عرض/تعديل";
                btnView.ToolTip = "عرض أو تعديل شركة النقل المحددة (Enter)";

                // زر تحديث
                btnRefresh.Text = "🔄 تحديث";
                btnRefresh.ToolTip = "تحديث البيانات (F5)";

                // زر طباعة
                btnPrint.Text = "🖨️ طباعة";
                btnPrint.ToolTip = "طباعة القائمة (Ctrl+P)";

                // زر تصدير إلى Excel
                btnExportToExcel.Text = "📊 تصدير إلى Excel";
                btnExportToExcel.ToolTip = "تصدير البيانات إلى Excel (Ctrl+E)";

                // حقل البحث
                lblSearch.Text = "🔍 البحث:";
                txtSearch.Properties.NullText = "ابحث عن شركة نقل...";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد أزرار شريط الأدوات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات بشكل غير متزامن
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                gridControl1.DataSource = null;
                
                // تحميل البيانات من قاعدة البيانات
                _allTransportCompanies = await _context.TransportCompanies
                    .OrderBy(tc => tc.Name)
                    .ToListAsync();

                // ربط البيانات بالشبكة
                gridControl1.DataSource = _allTransportCompanies;
                
                // تحديث عداد السجلات
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تحميل بيانات شركات النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في تحميل البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحديث عداد السجلات
        /// </summary>
        private void UpdateRecordCount()
        {
            try
            {
                int totalCount = _allTransportCompanies?.Count ?? 0;
                int filteredCount = gridView1.DataRowCount;

                if (totalCount == filteredCount)
                {
                    labelControl1.Text = $"🚛 إدارة شركات النقل - إجمالي الشركات: {totalCount}";
                }
                else
                {
                    labelControl1.Text = $"🚛 إدارة شركات النقل - عرض: {filteredCount} من أصل: {totalCount}";
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحديث العداد
            }
        }

        #endregion

        #region Search Methods

        /// <summary>
        /// البحث في البيانات
        /// </summary>
        private void SearchData()
        {
            try
            {
                string searchText = txtSearch.Text?.Trim() ?? "";

                if (string.IsNullOrEmpty(searchText))
                {
                    // عرض جميع البيانات
                    gridControl1.DataSource = _allTransportCompanies;
                }
                else
                {
                    // البحث في البيانات
                    var filteredData = _allTransportCompanies.Where(tc =>
                        (!string.IsNullOrEmpty(tc.Name) && tc.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                        (!string.IsNullOrEmpty(tc.ContactDetails) && tc.ContactDetails.Contains(searchText, StringComparison.OrdinalIgnoreCase)) ||
                        tc.TransportCompanyId.ToString().Contains(searchText)
                    ).ToList();

                    gridControl1.DataSource = filteredData;
                }

                // تحديث عداد السجلات
                UpdateRecordCount();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Form Events

        /// <summary>
        /// حدث تحميل النموذج
        /// </summary>
        private void TransportCompaniesForm_Load(object sender, EventArgs e)
        {
            try
            {
                // التركيز على حقل البحث
                txtSearch.Focus();
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء التحميل البسيطة
            }
        }

        /// <summary>
        /// حدث الضغط على مفاتيح النموذج
        /// </summary>
        private void TransportCompaniesForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.N)
            {
                btnNew_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.P)
            {
                btnPrint_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.E)
            {
                btnExportToExcel_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.F5)
            {
                btnRefresh_Click(sender, e);
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.F3)
            {
                txtSearch.Focus();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث تغيير حجم النموذج
        /// </summary>
        private void TransportCompaniesForm_Resize(object sender, EventArgs e)
        {
            try
            {
                // إعادة ضبط عرض الأعمدة عند تغيير حجم النموذج
                if (gridView1.Columns.Count > 0)
                {
                    gridView1.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تغيير الحجم
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// إضافة شركة نقل جديدة
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            if (_isFormOpening) return;

            try
            {
                _isFormOpening = true;

                var form = new TransportCompanyAddEditForm();
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج إضافة شركة نقل جديدة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isFormOpening = false;
            }
        }

        /// <summary>
        /// عرض/تعديل شركة النقل المحددة
        /// </summary>
        private void btnView_Click(object sender, EventArgs e)
        {
            if (_isFormOpening) return;

            try
            {
                var selectedTransportCompany = GetSelectedTransportCompany();
                if (selectedTransportCompany == null)
                {
                    MessageBox.Show("يرجى اختيار شركة نقل للعرض أو التعديل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                _isFormOpening = true;

                var form = new TransportCompanyAddEditForm(selectedTransportCompany);
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نموذج عرض/تعديل شركة النقل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isFormOpening = false;
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadDataAsync();
                MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// طباعة البيانات
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                gridControl1.ShowPrintPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تصدير البيانات إلى Excel
        /// </summary>
        private void btnExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx";
                    saveDialog.Title = "تصدير شركات النقل إلى Excel";
                    saveDialog.FileName = $"شركات_النقل_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        gridControl1.ExportToXlsx(saveDialog.FileName);
                        MessageBox.Show("تم تصدير البيانات بنجاح", "تصدير",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Search Events

        /// <summary>
        /// حدث تغيير نص البحث
        /// </summary>
        private void txtSearch_EditValueChanged(object sender, EventArgs e)
        {
            SearchData();
        }

        #endregion

        #region Grid Events

        /// <summary>
        /// حدث النقر المزدوج على الشبكة
        /// </summary>
        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, e);
        }

        /// <summary>
        /// حدث الضغط على مفاتيح الشبكة
        /// </summary>
        private void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnView_Click(sender, e);
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Delete)
            {
                // يمكن إضافة وظيفة الحذف هنا لاحقاً
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث تغيير مصدر البيانات للشبكة
        /// </summary>
        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            UpdateRecordCount();
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// الحصول على شركة النقل المحددة
        /// </summary>
        private TransportCompany? GetSelectedTransportCompany()
        {
            try
            {
                int selectedRowHandle = gridView1.FocusedRowHandle;
                if (selectedRowHandle >= 0)
                {
                    return gridView1.GetRow(selectedRowHandle) as TransportCompany;
                }
                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحصول على شركة النقل المحددة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }

        #endregion

        #region Dispose

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
