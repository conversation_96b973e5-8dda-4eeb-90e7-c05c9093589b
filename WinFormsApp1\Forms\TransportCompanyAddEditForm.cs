using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل شركات النقل
    /// </summary>
    public partial class TransportCompanyAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة شركة نقل جديدة
        /// </summary>
        public TransportCompanyAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = new TransportCompany();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل شركة نقل موجودة
        /// </summary>
        public TransportCompanyAddEditForm(TransportCompany selectedTransportCompany)
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = selectedTransportCompany;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var transportCompanies = await _context!.TransportCompanies
                    .Include(tc => tc.CreatedByUser)
                    .Include(tc => tc.ModifiedByUser)
                    .OrderBy(tc => tc.Name)
                    .ToListAsync();

                return transportCompanies.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل شركات النقل من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new TransportCompany
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                txtName.Text = transportCompany.Name ?? "";
                memoContactDetails.Text = transportCompany.ContactDetails ?? "";
                
                // عرض معلومات الإنشاء والتعديل
                if (transportCompany.TransportCompanyId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {transportCompany.CreatedAt:dd/MM/yyyy HH:mm}";
                    if (transportCompany.ModifiedAt.HasValue)
                    {
                        lblModifiedAt.Text = $"تاريخ التعديل: {transportCompany.ModifiedAt:dd/MM/yyyy HH:mm}";
                        lblModifiedAt.Visible = true;
                    }
                    else
                    {
                        lblModifiedAt.Visible = false;
                    }
                }
                else
                {
                    lblCreatedAt.Text = "";
                    lblModifiedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل البيانات من عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is TransportCompany transportCompany)
            {
                // تسجيل معلومات التشخيص
                System.Diagnostics.Debug.WriteLine($"تحميل البيانات من عناصر التحكم - ID: {transportCompany.TransportCompanyId}");
                System.Diagnostics.Debug.WriteLine($"اسم الشركة من النموذج: '{txtName.Text}'");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الاتصال من النموذج: '{memoContactDetails.Text}'");

                // تحديث البيانات الأساسية
                transportCompany.Name = string.IsNullOrWhiteSpace(txtName.Text) ? null : txtName.Text.Trim();
                transportCompany.ContactDetails = string.IsNullOrWhiteSpace(memoContactDetails.Text) ? null : memoContactDetails.Text.Trim();

                // تسجيل البيانات بعد التحديث
                System.Diagnostics.Debug.WriteLine($"اسم الشركة بعد التحديث: '{transportCompany.Name}'");
                System.Diagnostics.Debug.WriteLine($"تفاصيل الاتصال بعد التحديث: '{transportCompany.ContactDetails}'");

                // ملاحظة: تواريخ الإنشاء والتعديل يتم تعيينها في SaveEntityToDatabase
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("_currentEntity ليس من نوع TransportCompany");
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء التحقق من صحة البيانات");

                // التحقق من اسم شركة النقل
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    System.Diagnostics.Debug.WriteLine("خطأ: اسم شركة النقل فارغ");
                    XtraMessageBox.Show("يرجى إدخال اسم شركة النقل", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول اسم شركة النقل
                if (txtName.Text.Trim().Length > 100)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ: اسم شركة النقل طويل جداً ({txtName.Text.Trim().Length} حرف)");
                    XtraMessageBox.Show("اسم شركة النقل يجب أن يكون أقل من 100 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول تفاصيل الاتصال
                if (!string.IsNullOrWhiteSpace(memoContactDetails.Text) && memoContactDetails.Text.Trim().Length > 500)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ: تفاصيل الاتصال طويلة جداً ({memoContactDetails.Text.Trim().Length} حرف)");
                    XtraMessageBox.Show("تفاصيل الاتصال يجب أن تكون أقل من 500 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    memoContactDetails.Focus();
                    return false;
                }

                System.Diagnostics.Debug.WriteLine("التحقق من صحة البيانات نجح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من صحة البيانات: {ex.Message}");
                HandleError(ex, "التحقق من صحة البيانات");
                return false;
            }
        }

        /// <summary>
        /// حفظ الكائن في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            try
            {
                if (entity is TransportCompany transportCompany)
                {
                    // تسجيل معلومات التشخيص
                    System.Diagnostics.Debug.WriteLine($"حفظ شركة النقل - ID: {transportCompany.TransportCompanyId}, Name: {transportCompany.Name}");

                    if (transportCompany.TransportCompanyId == 0)
                    {
                        // إضافة جديدة
                        System.Diagnostics.Debug.WriteLine("إضافة شركة نقل جديدة");
                        transportCompany.CreatedAt = DateTime.Now;
                        transportCompany.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.TransportCompanies.Add(transportCompany);
                    }
                    else
                    {
                        // تعديل موجود
                        System.Diagnostics.Debug.WriteLine("تعديل شركة نقل موجودة");
                        transportCompany.ModifiedAt = DateTime.Now;
                        transportCompany.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.TransportCompanies.Update(transportCompany);
                    }

                    int result = await _context!.SaveChangesAsync();
                    System.Diagnostics.Debug.WriteLine($"تم حفظ {result} سجل");

                    return result > 0;
                }

                System.Diagnostics.Debug.WriteLine("الكائن ليس من نوع TransportCompany");
                return false;
            }
            catch (Exception ex)
            {
                // تسجيل تفاصيل الخطأ
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ شركة النقل: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }

                HandleError(ex, "حفظ شركة النقل");
                return false;
            }
        }

        /// <summary>
        /// حذف الكائن من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            try
            {
                if (entity is TransportCompany transportCompany && transportCompany.TransportCompanyId > 0)
                {
                    _context!.TransportCompanies.Remove(transportCompany);
                    await _context!.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف شركة النقل");
                return false;
            }
        }





        #endregion
    }
}
