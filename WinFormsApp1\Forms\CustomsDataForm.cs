using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// فورم عرض جدول البيانات الجمركية مع عمليات CRUD
    /// </summary>
    public partial class CustomsDataForm : XtraForm
    {
        #region Fields

        private readonly WarehouseDbContext _context;
        private bool _isFormOpening = false;
        private List<CustomsD> _allCustomsData = new List<CustomsD>();

        #endregion

        #region Constructor

        public CustomsDataForm()
        {
            try
            {
                InitializeComponent();
                _context = DatabaseService.GetDbContext();
                SetupForm();
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج البيانات الجمركية:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Setup

        /// <summary>
        /// إعداد النموذج الأساسي
        /// </summary>
        private void SetupForm()
        {
            this.Text = "📋 إدارة البيانات الجمركية";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // إعداد نصوص الأزرار
            btnNew.Text = "🆕 جديد";
            btnView.Text = "👁️ عرض";
            btnPrint.Text = "🖨️ طباعة";
            btnExportToExcel.Text = "📊 تصدير Excel";
            btnRefresh.Text = "🔄 تحديث";

            // إعداد التلميحات
            btnNew.ToolTip = "إنشاء بيان جمركي جديد (Ctrl+N)";
            btnView.ToolTip = "عرض/تعديل البيان الجمركي المحدد (Enter)";
            btnPrint.ToolTip = "طباعة قائمة البيانات الجمركية (Ctrl+P)";
            btnExportToExcel.ToolTip = "تصدير البيانات إلى Excel (Ctrl+E)";
            btnRefresh.ToolTip = "تحديث البيانات (Ctrl+F5)";

            // إعداد حقل البحث
            txtSearch.Properties.NullText = "🔍 البحث في البيانات الجمركية...";

            SetupGrid();
            SetupEvents();
        }

        /// <summary>
        /// إعداد الأحداث
        /// </summary>
        private void SetupEvents()
        {
            this.KeyPreview = true;
            this.KeyDown += CustomsDataForm_KeyDown;
            this.Resize += CustomsDataForm_Resize;

            // أحداث الأزرار
            btnNew.Click += btnNew_Click;
            btnView.Click += btnView_Click;
            btnRefresh.Click += btnRefresh_Click;
            btnPrint.Click += btnPrint_Click;
            btnExportToExcel.Click += btnExportToExcel_Click;

            // أحداث البحث
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;

            // أحداث الشبكة
            gridView1.DoubleClick += GridView1_DoubleClick;
            gridView1.KeyDown += GridView1_KeyDown;
            gridView1.DataSourceChanged += GridView1_DataSourceChanged;
        }

        /// <summary>
        /// إعداد الشبكة والأعمدة
        /// </summary>
        private void SetupGrid()
        {
            // إعدادات عامة للشبكة
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ShowIndicator = true;
            gridView1.OptionsView.ShowVerticalLines = DevExpress.Utils.DefaultBoolean.True;
            gridView1.OptionsView.ShowHorizontalLines = DevExpress.Utils.DefaultBoolean.True;
            gridView1.OptionsView.RowAutoHeight = true;
            gridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full;
            gridView1.OptionsView.BestFitMaxRowCount = 100;
            gridView1.HorzScrollVisibility = DevExpress.XtraGrid.Views.Base.ScrollVisibility.Never;

            // إعداد الأعمدة للبيانات الجمركية
            colCustomsDataId.Caption = "رقم البيان";
            colCustomsDataId.MinWidth = 80;
            colCustomsDataId.MaxWidth = 120;
            colCustomsDataId.OptionsColumn.AllowEdit = false;
            colCustomsDataId.OptionsColumn.FixedWidth = false;

            colCustomsNumber.Caption = "رقم الجمرك";
            colCustomsNumber.MinWidth = 120;
            colCustomsNumber.OptionsColumn.AllowEdit = false;
            colCustomsNumber.OptionsColumn.FixedWidth = false;

            colCustomsDate.Caption = "تاريخ الجمرك";
            colCustomsDate.MinWidth = 120;
            colCustomsDate.MaxWidth = 150;
            colCustomsDate.OptionsColumn.AllowEdit = false;
            colCustomsDate.OptionsColumn.FixedWidth = false;
            colCustomsDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colCustomsDate.DisplayFormat.FormatString = "yyyy/MM/dd";

            colCustomsType.Caption = "نوع الجمرك";
            colCustomsType.MinWidth = 100;
            colCustomsType.MaxWidth = 150;
            colCustomsType.OptionsColumn.AllowEdit = false;
            colCustomsType.OptionsColumn.FixedWidth = false;

            colMaterialName.Caption = "المادة";
            colMaterialName.MinWidth = 150;
            colMaterialName.OptionsColumn.AllowEdit = false;
            colMaterialName.OptionsColumn.FixedWidth = false;

            colImporterName.Caption = "المستورد";
            colImporterName.MinWidth = 150;
            colImporterName.OptionsColumn.AllowEdit = false;
            colImporterName.OptionsColumn.FixedWidth = false;

            colVesselName.Caption = "الباخرة";
            colVesselName.MinWidth = 120;
            colVesselName.OptionsColumn.AllowEdit = false;
            colVesselName.OptionsColumn.FixedWidth = false;

            colCommonName.Caption = "الاسم الشائع";
            colCommonName.MinWidth = 120;
            colCommonName.OptionsColumn.AllowEdit = false;
            colCommonName.OptionsColumn.FixedWidth = false;

            colQuantity.Caption = "الكمية";
            colQuantity.MinWidth = 80;
            colQuantity.MaxWidth = 120;
            colQuantity.OptionsColumn.AllowEdit = false;
            colQuantity.OptionsColumn.FixedWidth = false;
            colQuantity.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            colQuantity.DisplayFormat.FormatString = "N4";

            colCreatedAt.Caption = "تاريخ الإنشاء";
            colCreatedAt.MinWidth = 130;
            colCreatedAt.MaxWidth = 180;
            colCreatedAt.OptionsColumn.AllowEdit = false;
            colCreatedAt.OptionsColumn.FixedWidth = false;
            colCreatedAt.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            colCreatedAt.DisplayFormat.FormatString = "yyyy/MM/dd HH:mm";

            // تطبيق أفضل توزيع للأعمدة
            gridView1.BestFitColumns();
        }

        #endregion

        #region Data Management

        /// <summary>
        /// تحميل البيانات الجمركية
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                gridControl1.Enabled = false;

                _allCustomsData = await _context.CustomsDs
                    .Include(c => c.Material)
                    .Include(c => c.Importer)
                    .Include(c => c.Vessel)
                    .Include(c => c.CreatedByUser)
                    .Include(c => c.ModifiedByUser)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();

                ApplySearch();
                UpdateRecordCount(_allCustomsData.Count);
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات الجمركية");
            }
            finally
            {
                gridControl1.Enabled = true;
            }
        }

        /// <summary>
        /// تحديث عدد السجلات
        /// </summary>
        private void UpdateRecordCount(int count)
        {
            labelControl1.Text = $"📋 إدارة البيانات الجمركية - إجمالي البيانات: {count:N0}";
        }

        /// <summary>
        /// تطبيق البحث على البيانات
        /// </summary>
        private void ApplySearch()
        {
            try
            {
                var searchText = txtSearch.Text?.Trim().ToLower() ?? "";
                
                List<CustomsD> filteredData;
                if (string.IsNullOrEmpty(searchText))
                {
                    filteredData = _allCustomsData;
                }
                else
                {
                    filteredData = _allCustomsData.Where(c =>
                        (c.CustomsNumber?.ToLower().Contains(searchText) == true) ||
                        (c.CustomsType?.ToLower().Contains(searchText) == true) ||
                        (c.Material?.MaterialName?.ToLower().Contains(searchText) == true) ||
                        (c.Importer?.Name?.ToLower().Contains(searchText) == true) ||
                        (c.Vessel?.VesselName?.ToLower().Contains(searchText) == true) ||
                        (c.CommonName?.ToLower().Contains(searchText) == true) ||
                        c.CustomsDataId.ToString().Contains(searchText)
                    ).ToList();
                }

                gridControl1.DataSource = filteredData;
                UpdateSearchResultsCount(filteredData.Count, _allCustomsData.Count);
            }
            catch (Exception ex)
            {
                HandleError(ex, "البحث");
            }
        }

        /// <summary>
        /// تحديث عدد نتائج البحث
        /// </summary>
        private void UpdateSearchResultsCount(int filteredCount, int totalCount)
        {
            if (filteredCount == totalCount)
            {
                UpdateRecordCount(totalCount);
            }
            else
            {
                labelControl1.Text = $"📋 إدارة البيانات الجمركية - نتائج البحث: {filteredCount:N0} من أصل {totalCount:N0}";
            }
        }

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        private void HandleError(Exception ex, string operation)
        {
            string message = $"خطأ في {operation}:\n{ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
            }
            XtraMessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Search Events

        /// <summary>
        /// حدث تغيير نص البحث
        /// </summary>
        private void txtSearch_EditValueChanged(object sender, EventArgs e)
        {
            ApplySearch();
        }

        #endregion

        #region Button Events

        /// <summary>
        /// إضافة بيان جمركي جديد
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            if (_isFormOpening) return;

            try
            {
                _isFormOpening = true;

                var form = new CustomsDataAddEditForm();
                if (form.ShowDialog() == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "إضافة بيان جمركي جديد");
            }
            finally
            {
                _isFormOpening = false;
            }
        }

        /// <summary>
        /// عرض/تعديل البيان الجمركي المحدد
        /// </summary>
        private void btnView_Click(object sender, EventArgs e)
        {

        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataAsync();
        }

        /// <summary>
        /// طباعة البيانات
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                gridControl1.ShowPrintPreview();
            }
            catch (Exception ex)
            {
                HandleError(ex, "طباعة البيانات");
            }
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void btnExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx";
                    saveDialog.Title = "تصدير البيانات الجمركية إلى Excel";
                    saveDialog.FileName = $"البيانات_الجمركية_{DateTime.Now:yyyy-MM-dd}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        gridControl1.ExportToXlsx(saveDialog.FileName);
                        XtraMessageBox.Show("تم تصدير البيانات بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تصدير البيانات");
            }
        }

        #endregion

        #region Grid Events

        /// <summary>
        /// النقر المزدوج على الشبكة
        /// </summary>
        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, e);
        }

        /// <summary>
        /// ضغط مفتاح في الشبكة
        /// </summary>
        private void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnView_Click(sender, e);
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Delete)
            {
                DeleteSelectedCustomsData();
                e.Handled = true;
            }
        }

        /// <summary>
        /// تغيير مصدر البيانات
        /// </summary>
        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            // يمكن إضافة منطق إضافي هنا عند الحاجة
        }

        /// <summary>
        /// حذف البيان الجمركي المحدد
        /// </summary>
        private async void DeleteSelectedCustomsData()
        {
            try
            {
                var selectedCustomsData = gridView1.GetFocusedRow() as CustomsD;
                if (selectedCustomsData != null)
                {
                    var result = XtraMessageBox.Show(
                        $"هل أنت متأكد من حذف البيان الجمركي رقم '{selectedCustomsData.CustomsNumber}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        _context.CustomsDs.Remove(selectedCustomsData);
                        await _context.SaveChangesAsync();

                        XtraMessageBox.Show("تم حذف البيان الجمركي بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        LoadDataAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف البيان الجمركي");
            }
        }

        #endregion

        #region Form Events

        /// <summary>
        /// ضغط مفتاح في النموذج
        /// </summary>
        private void CustomsDataForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.N)
            {
                btnNew_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.P)
            {
                btnPrint_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.E)
            {
                btnExportToExcel_Click(sender, e);
                e.Handled = true;
            }
            else if (e.Control && e.KeyCode == Keys.F5)
            {
                btnRefresh_Click(sender, e);
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.F3)
            {
                txtSearch.Focus();
                e.Handled = true;
            }
        }

        /// <summary>
        /// معالج حدث تغيير حجم النافذة
        /// </summary>
        private void CustomsDataForm_Resize(object sender, EventArgs e)
        {
            // إعادة تعديل عرض الأعمدة عند تغيير حجم النافذة
            if (gridView1 != null && this.WindowState != FormWindowState.Minimized && gridControl1.Width > 0)
            {
                // تأخير قصير لضمان اكتمال تغيير الحجم
                var timer = new System.Windows.Forms.Timer();
                timer.Interval = 100;
                timer.Tick += (s, args) =>
                {
                    timer.Stop();
                    timer.Dispose();
                    try
                    {
                        gridView1.BestFitColumns();
                    }
                    catch
                    {
                        // تجاهل الأخطاء في حالة عدم توفر البيانات
                    }
                };
                timer.Start();
            }
        }

        #endregion
    }
}
