using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل آليات النقل
    /// </summary>
    public partial class VehicleAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة آلية نقل جديدة
        /// </summary>
        public VehicleAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة آليات النقل";

                _currentEntity = new Vehicle();
                SetMode(FormMode.Add);

                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();

                // تحميل البيانات المرجعية بعد تهيئة النموذج
                this.Load += async (s, e) => await LoadReferenceDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة آلية النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل آلية نقل موجودة
        /// </summary>
        public VehicleAddEditForm(Vehicle selectedVehicle)
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة آليات النقل";

                _currentEntity = selectedVehicle;
                SetMode(FormMode.Edit);

                LoadEntityToControls();
                _ = LoadDataListAsync();

                // تحميل البيانات المرجعية بعد تهيئة النموذج
                this.Load += async (s, e) => await LoadReferenceDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل آلية النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، نتجاهل التنقل ونبقى في وضع الإضافة البسيط
                _dataList.Clear();
                _currentIndex = -1;
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// تحميل البيانات المرجعية (شركات النقل وأنواع الآليات)
        /// </summary>
        private async Task LoadReferenceDataAsync()
        {
            try
            {
                // تحميل شركات النقل
                var transportCompanies = await _context!.TransportCompanies
                    .OrderBy(tc => tc.Name)
                    .ToListAsync();

                cmbTransportCompany.Properties.DataSource = transportCompanies;
                cmbTransportCompany.Properties.DisplayMember = "Name";
                cmbTransportCompany.Properties.ValueMember = "TransportCompanyId";
                cmbTransportCompany.Properties.NullText = "اختر شركة النقل...";

                // تحميل أنواع الآليات
                var vehicleTypes = await _context.VehicleTypes
                    .OrderBy(vt => vt.VehicleTypeName)
                    .ToListAsync();

                cmbVehicleType.Properties.DataSource = vehicleTypes;
                cmbVehicleType.Properties.DisplayMember = "VehicleTypeName";
                cmbVehicleType.Properties.ValueMember = "VehicleTypeId";
                cmbVehicleType.Properties.NullText = "اختر نوع الآلية...";
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات المرجعية");
            }
        }

        #endregion

        #region Virtual Methods Override

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Vehicle
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Vehicle vehicle)
            {
                txtVehicleName.Text = vehicle.VehicleName;
                cmbTransportCompany.EditValue = vehicle.TransportCompanyId;
                cmbVehicleType.EditValue = vehicle.VehicleTypeId;
                memoDescription.Text = vehicle.Description;
            }
        }

        /// <summary>
        /// تحميل البيانات من عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Vehicle vehicle)
            {
                vehicle.VehicleName = txtVehicleName.Text.Trim();
                vehicle.TransportCompanyId = Convert.ToInt32(cmbTransportCompany.EditValue ?? 0);
                vehicle.VehicleTypeId = Convert.ToInt32(cmbVehicleType.EditValue ?? 0);
                vehicle.Description = memoDescription.Text.Trim();
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            var errors = new List<string>();

            // التحقق من اسم الآلية
            if (string.IsNullOrWhiteSpace(txtVehicleName.Text))
                errors.Add("• يرجى إدخال اسم آلية النقل");
            else if (txtVehicleName.Text.Trim().Length < 2)
                errors.Add("• اسم آلية النقل يجب أن يكون حرفين على الأقل");

            // التحقق من شركة النقل
            if (cmbTransportCompany.EditValue == null || Convert.ToInt32(cmbTransportCompany.EditValue) == 0)
                errors.Add("• يرجى اختيار شركة النقل");

            // التحقق من نوع الآلية
            if (cmbVehicleType.EditValue == null || Convert.ToInt32(cmbVehicleType.EditValue) == 0)
                errors.Add("• يرجى اختيار نوع آلية النقل");

            // التحقق من عدم تكرار اسم الآلية لنفس الشركة
            if (!string.IsNullOrWhiteSpace(txtVehicleName.Text) && 
                cmbTransportCompany.EditValue != null && Convert.ToInt32(cmbTransportCompany.EditValue) > 0)
            {
                int currentVehicleId = (_currentEntity is Vehicle currentVehicle) ? currentVehicle.VehicleId : 0;
                int transportCompanyId = Convert.ToInt32(cmbTransportCompany.EditValue);
                
                var existingVehicle = _context!.Vehicles.FirstOrDefault(v =>
                    v.VehicleName == txtVehicleName.Text.Trim() && 
                    v.TransportCompanyId == transportCompanyId &&
                    v.VehicleId != currentVehicleId);

                if (existingVehicle != null)
                    errors.Add("• اسم آلية النقل موجود مسبقاً لنفس شركة النقل");
            }

            if (errors.Any())
            {
                string message = "يرجى تصحيح الأخطاء التالية:\n\n" + string.Join("\n", errors);
                XtraMessageBox.Show(message, "خطأ في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// حفظ آلية النقل في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Vehicle vehicle)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        vehicle.CreatedAt = DateTime.Now;
                        vehicle.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Vehicles.Add(vehicle);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingVehicle = await _context!.Vehicles.FindAsync(vehicle.VehicleId);
                        if (existingVehicle != null)
                        {
                            existingVehicle.VehicleName = vehicle.VehicleName;
                            existingVehicle.TransportCompanyId = vehicle.TransportCompanyId;
                            existingVehicle.VehicleTypeId = vehicle.VehicleTypeId;
                            existingVehicle.Description = vehicle.Description;
                            existingVehicle.ModifiedAt = DateTime.Now;
                            existingVehicle.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Vehicles.Update(existingVehicle);
                        }
                        else
                        {
                            return false; // آلية النقل غير موجودة
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ آلية النقل");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف آلية النقل من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Vehicle vehicle)
            {
                try
                {
                    // البحث عن آلية النقل في قاعدة البيانات للتأكد من وجودها
                    var existingVehicle = await _context!.Vehicles.FindAsync(vehicle.VehicleId);
                    if (existingVehicle != null)
                    {
                        _context.Vehicles.Remove(existingVehicle);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("آلية النقل غير موجودة"), "حذف آلية النقل");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف آلية النقل");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Vehicle vehicle)
            {
                return $"هل أنت متأكد من حذف آلية النقل '{vehicle.VehicleName}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف آلية النقل؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            if (_currentEntity is Vehicle vehicle)
            {
                return isEdit ? 
                    $"تم تحديث آلية النقل '{vehicle.VehicleName}' بنجاح" : 
                    $"تم إنشاء آلية النقل '{vehicle.VehicleName}' بنجاح";
            }
            return isEdit ? "تم تحديث آلية النقل بنجاح" : "تم إنشاء آلية النقل بنجاح";
        }

        /// <summary>
        /// تحميل قائمة البيانات للتنقل
        /// </summary>
        public override async Task LoadDataListAsync()
        {
            try
            {
                _dataList = await _context!.Vehicles
                    .Include(v => v.TransportCompany)
                    .Include(v => v.VehicleType)
                    .Include(v => v.CreatedByUser)
                    .Include(v => v.ModifiedByUser)
                    .OrderBy(v => v.VehicleName)
                    .Cast<object>()
                    .ToListAsync();

                // تحديد الموقع الحالي إذا كان في وضع التعديل
                if (CurrentMode == FormMode.Edit && _currentEntity is Vehicle currentVehicle)
                {
                    _currentIndex = _dataList.FindIndex(v => ((Vehicle)v).VehicleId == currentVehicle.VehicleId);
                    if (_currentIndex < 0) _currentIndex = 0;
                }
                else
                {
                    _currentIndex = 0;
                }

                UpdateNavigationInfo();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل قائمة آليات النقل");
            }
        }

        #endregion

        #region Error Handling

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        protected override void HandleError(Exception ex, string operation)
        {
            string message = $"خطأ في {operation}:\n{ex.Message}";
            XtraMessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
