using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل المستودعات
    /// </summary>
    public partial class WarehouseAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة مستودع جديد
        /// </summary>
        public WarehouseAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🏭 إدارة المستودعات";

                _currentEntity = new Warehouse();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة المستودع:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل مستودع موجود
        /// </summary>
        public WarehouseAddEditForm(Warehouse selectedWarehouse)
        {
            try
            {
                InitializeComponent();
                this.Text = "🏭 إدارة المستودعات";

                _currentEntity = selectedWarehouse;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل المستودع:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var warehouses = await _context!.Warehouses
                    .Include(w => w.CreatedByUser)
                    .Include(w => w.ModifiedByUser)
                    .OrderBy(w => w.WarehouseName)
                    .ToListAsync();

                return warehouses.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل المستودعات من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Warehouse
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Warehouse warehouse)
            {
                txtWarehouseName.Text = warehouse.WarehouseName ?? "";
                txtLocation.Text = warehouse.Location ?? "";
                memoDescription.Text = warehouse.Description ?? "";
                
                // عرض تاريخ الإنشاء
                if (warehouse.WarehouseId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {warehouse.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Warehouse warehouse)
            {
                warehouse.WarehouseName = txtWarehouseName.Text.Trim();
                warehouse.Location = string.IsNullOrWhiteSpace(txtLocation.Text) ? null : txtLocation.Text.Trim();
                warehouse.Description = string.IsNullOrWhiteSpace(memoDescription.Text) ? null : memoDescription.Text.Trim();
                
                // تعيين تاريخ الإنشاء للمستودعات الجديدة
                if (warehouse.WarehouseId == 0)
                {
                    warehouse.CreatedAt = DateTime.Now;
                }
                else
                {
                    warehouse.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم المستودع
            if (string.IsNullOrWhiteSpace(txtWarehouseName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم المستودع", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtWarehouseName.Focus();
                return false;
            }

            // التحقق من طول اسم المستودع
            if (txtWarehouseName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم المستودع لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtWarehouseName.Focus();
                return false;
            }

            // التحقق من طول الموقع
            if (!string.IsNullOrWhiteSpace(txtLocation.Text) && txtLocation.Text.Trim().Length > 300)
            {
                XtraMessageBox.Show("الموقع لا يجب أن يتجاوز 300 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLocation.Focus();
                return false;
            }

            // التحقق من طول الوصف
            if (!string.IsNullOrWhiteSpace(memoDescription.Text) && memoDescription.Text.Trim().Length > 500)
            {
                XtraMessageBox.Show("الوصف لا يجب أن يتجاوز 500 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                memoDescription.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم المستودع
            if (_currentEntity is Warehouse currentWarehouse)
            {
                var existingWarehouse = _context!.Warehouses.FirstOrDefault(w => 
                    w.WarehouseName == txtWarehouseName.Text.Trim() && w.WarehouseId != currentWarehouse.WarehouseId);
                
                if (existingWarehouse != null)
                {
                    XtraMessageBox.Show("اسم المستودع موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtWarehouseName.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// حفظ المستودع في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Warehouse warehouse)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        warehouse.CreatedAt = DateTime.Now;
                        warehouse.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Warehouses.Add(warehouse);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingWarehouse = await _context!.Warehouses.FindAsync(warehouse.WarehouseId);
                        if (existingWarehouse != null)
                        {
                            existingWarehouse.WarehouseName = warehouse.WarehouseName;
                            existingWarehouse.Location = warehouse.Location;
                            existingWarehouse.Description = warehouse.Description;
                            existingWarehouse.ModifiedAt = DateTime.Now;
                            existingWarehouse.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Warehouses.Update(existingWarehouse);
                        }
                        else
                        {
                            return false; // المستودع غير موجود
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ المستودع");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف المستودع من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Warehouse warehouse)
            {
                try
                {
                    // البحث عن المستودع في قاعدة البيانات للتأكد من وجوده
                    var existingWarehouse = await _context!.Warehouses.FindAsync(warehouse.WarehouseId);
                    if (existingWarehouse != null)
                    {
                        _context.Warehouses.Remove(existingWarehouse);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("المستودع غير موجود"), "حذف المستودع");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف المستودع");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Warehouse warehouse)
            {
                return $"هل أنت متأكد من حذف المستودع '{warehouse.WarehouseName}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذا المستودع؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث بيانات المستودع بنجاح" : "تم إضافة المستودع الجديد بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Warehouse warehouse)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "🏭 إضافة مستودع جديد",
                    FormMode.Edit => $"🏭 تعديل المستودع: {warehouse.WarehouseName}",
                    FormMode.View => $"🏭 عرض المستودع: {warehouse.WarehouseName}",
                    _ => "🏭 إدارة المستودعات"
                };
            }
            else
            {
                this.Text = "🏭 إدارة المستودعات";
            }
        }

        #endregion
    }
}
