using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل المستوردين
    /// </summary>
    public partial class ImporterAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة مستورد جديد
        /// </summary>
        public ImporterAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "📦 إدارة المستوردين";

                _currentEntity = new Importer();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة المستورد:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل مستورد موجود
        /// </summary>
        public ImporterAddEditForm(Importer selectedImporter)
        {
            try
            {
                InitializeComponent();
                this.Text = "📦 إدارة المستوردين";

                _currentEntity = selectedImporter;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل المستورد:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var importers = await _context!.Importers
                    .Include(i => i.CreatedByUser)
                    .Include(i => i.ModifiedByUser)
                    .OrderBy(i => i.Name)
                    .ToListAsync();

                return importers.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل المستوردين من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Importer
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Importer importer)
            {
                txtName.Text = importer.Name ?? "";
                txtTaxNumber.Text = importer.TaxNumber ?? "";
                
                // عرض تاريخ الإنشاء
                if (importer.ImporterId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {importer.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Importer importer)
            {
                importer.Name = txtName.Text.Trim();
                importer.TaxNumber = string.IsNullOrWhiteSpace(txtTaxNumber.Text) ? null : txtTaxNumber.Text.Trim();
                
                // تعيين تاريخ الإنشاء للمستوردين الجدد
                if (importer.ImporterId == 0)
                {
                    importer.CreatedAt = DateTime.Now;
                }
                else
                {
                    importer.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم المستورد
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم المستورد", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول اسم المستورد
            if (txtName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم المستورد لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            // التحقق من طول الرقم الضريبي
            if (!string.IsNullOrWhiteSpace(txtTaxNumber.Text) && txtTaxNumber.Text.Trim().Length > 100)
            {
                XtraMessageBox.Show("الرقم الضريبي لا يجب أن يتجاوز 100 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTaxNumber.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم المستورد
            if (_currentEntity is Importer currentImporter)
            {
                var existingImporter = _context!.Importers.FirstOrDefault(i => 
                    i.Name == txtName.Text.Trim() && i.ImporterId != currentImporter.ImporterId);
                
                if (existingImporter != null)
                {
                    XtraMessageBox.Show("اسم المستورد موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }
            }

            // التحقق من عدم تكرار الرقم الضريبي (إذا تم إدخاله)
            if (!string.IsNullOrWhiteSpace(txtTaxNumber.Text))
            {
                if (_currentEntity is Importer currentImporter2)
                {
                    var existingTaxNumber = _context!.Importers.FirstOrDefault(i => 
                        i.TaxNumber == txtTaxNumber.Text.Trim() && i.ImporterId != currentImporter2.ImporterId);
                    
                    if (existingTaxNumber != null)
                    {
                        XtraMessageBox.Show("الرقم الضريبي موجود بالفعل، يرجى إدخال رقم آخر", "خطأ في البيانات",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtTaxNumber.Focus();
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// حفظ المستورد في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Importer importer)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        importer.CreatedAt = DateTime.Now;
                        importer.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Importers.Add(importer);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingImporter = await _context!.Importers.FindAsync(importer.ImporterId);
                        if (existingImporter != null)
                        {
                            existingImporter.Name = importer.Name;
                            existingImporter.TaxNumber = importer.TaxNumber;
                            existingImporter.ModifiedAt = DateTime.Now;
                            existingImporter.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Importers.Update(existingImporter);
                        }
                        else
                        {
                            return false; // المستورد غير موجود
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ المستورد");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف المستورد من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Importer importer)
            {
                try
                {
                    // البحث عن المستورد في قاعدة البيانات للتأكد من وجوده
                    var existingImporter = await _context!.Importers.FindAsync(importer.ImporterId);
                    if (existingImporter != null)
                    {
                        _context.Importers.Remove(existingImporter);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("المستورد غير موجود"), "حذف المستورد");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف المستورد");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Importer importer)
            {
                return $"هل أنت متأكد من حذف المستورد '{importer.Name}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذا المستورد؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث بيانات المستورد بنجاح" : "تم إضافة المستورد الجديد بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Importer importer)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "📦 إضافة مستورد جديد",
                    FormMode.Edit => $"📦 تعديل المستورد: {importer.Name}",
                    FormMode.View => $"📦 عرض المستورد: {importer.Name}",
                    _ => "📦 إدارة المستوردين"
                };
            }
            else
            {
                this.Text = "📦 إدارة المستوردين";
            }
        }

        #endregion
    }
}
