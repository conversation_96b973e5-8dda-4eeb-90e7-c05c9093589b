using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل البيانات الجمركية
    /// </summary>
    public partial class CustomsDataAddEditForm : MasterF
    {
        #region Constructors

        /// <summary>
        /// منشئ لإضافة بيان جمركي جديد
        /// </summary>
        public CustomsDataAddEditForm() : base()
        {
            this.Text = "📋 إدارة البيانات الجمركية - إضافة جديد";
            _currentEntity = CreateNewEntityInstance();
            SetMode(FormMode.Add);
        }

        /// <summary>
        /// منشئ لتعديل بيان جمركي موجود
        /// </summary>
        public CustomsDataAddEditForm(CustomsD selectedCustomsData) : base()
        {
            this.Text = "📋 إدارة البيانات الجمركية - تعديل";
            _currentEntity = selectedCustomsData;
            SetMode(FormMode.Edit);
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل البيانات المرجعية بعد تهيئة النموذج
        /// </summary>
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            try
            {
                // تأخير قصير للتأكد من تهيئة جميع العناصر
                this.BeginInvoke(new Action(() =>
                {
                    LoadReferenceDataSafe();
                    LoadEntityToControls();
                }));
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل النموذج");
            }
        }

        /// <summary>
        /// تحميل البيانات المرجعية بشكل آمن
        /// </summary>
        private void LoadReferenceDataSafe()
        {
            try
            {
                if (_context == null)
                {
                    XtraMessageBox.Show("خطأ في الاتصال بقاعدة البيانات", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // التحقق من وجود عناصر التحكم قبل استخدامها
                if (this.Controls.Find("cmbMaterial", true).FirstOrDefault() is DevExpress.XtraEditors.LookUpEdit materialCombo)
                {
                    var materials = _context.Materials
                        .OrderBy(m => m.MaterialName)
                        .ToList();
                    materialCombo.Properties.DataSource = materials;
                    materialCombo.Properties.DisplayMember = "MaterialName";
                    materialCombo.Properties.ValueMember = "MaterialId";
                }

                if (this.Controls.Find("cmbImporter", true).FirstOrDefault() is DevExpress.XtraEditors.LookUpEdit importerCombo)
                {
                    var importers = _context.Importers
                        .OrderBy(i => i.Name)
                        .ToList();
                    importerCombo.Properties.DataSource = importers;
                    importerCombo.Properties.DisplayMember = "Name";
                    importerCombo.Properties.ValueMember = "ImporterId";
                }

                if (this.Controls.Find("cmbVessel", true).FirstOrDefault() is DevExpress.XtraEditors.LookUpEdit vesselCombo)
                {
                    var vessels = _context.Vessels
                        .OrderBy(v => v.VesselName)
                        .ToList();
                    vesselCombo.Properties.DataSource = vessels;
                    vesselCombo.Properties.DisplayMember = "VesselName";
                    vesselCombo.Properties.ValueMember = "VesselId";
                }

                // إعداد قائمة أنواع الجمرك
                if (this.Controls.Find("cmbCustomsType", true).FirstOrDefault() is DevExpress.XtraEditors.ComboBoxEdit customsTypeCombo)
                {
                    customsTypeCombo.Properties.Items.Clear();
                    customsTypeCombo.Properties.Items.AddRange(new string[]
                    {
                        "استيراد",
                        "تصدير",
                        "ترانزيت",
                        "إعادة تصدير"
                    });
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات المرجعية");
            }
        }

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                if (_context == null) return new List<object>();

                var customsData = await _context.CustomsDs
                    .Include(c => c.Material)
                    .Include(c => c.Importer)
                    .Include(c => c.Vessel)
                    .OrderByDescending(c => c.CreatedAt)
                    .ToListAsync();

                return customsData.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات الجمركية من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new CustomsD
            {
                CustomsDate = DateOnly.FromDateTime(DateTime.Now),
                CreatedAt = DateTime.Now,
                Quantity = 1.0000m
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            try
            {
                if (_currentEntity is CustomsD customsData)
                {
                    // البحث الآمن عن عناصر التحكم
                    var txtCustomsNumber = this.Controls.Find("txtCustomsNumber", true).FirstOrDefault() as DevExpress.XtraEditors.TextEdit;
                    if (txtCustomsNumber != null)
                        txtCustomsNumber.Text = customsData.CustomsNumber ?? "";

                    var dateCustomsDate = this.Controls.Find("dateCustomsDate", true).FirstOrDefault() as DevExpress.XtraEditors.DateEdit;
                    if (dateCustomsDate != null)
                    {
                        if (customsData.CustomsDate != default(DateOnly))
                        {
                            dateCustomsDate.EditValue = customsData.CustomsDate.ToDateTime(TimeOnly.MinValue);
                        }
                        else
                        {
                            dateCustomsDate.EditValue = DateTime.Now;
                        }
                    }

                    var cmbCustomsType = this.Controls.Find("cmbCustomsType", true).FirstOrDefault() as DevExpress.XtraEditors.ComboBoxEdit;
                    if (cmbCustomsType != null)
                        cmbCustomsType.Text = customsData.CustomsType ?? "";

                    var txtCommonName = this.Controls.Find("txtCommonName", true).FirstOrDefault() as DevExpress.XtraEditors.TextEdit;
                    if (txtCommonName != null)
                        txtCommonName.Text = customsData.CommonName ?? "";

                    var spinQuantity = this.Controls.Find("spinQuantity", true).FirstOrDefault() as DevExpress.XtraEditors.SpinEdit;
                    if (spinQuantity != null)
                        spinQuantity.Value = customsData.Quantity;

                    // تعيين القيم للقوائم المنسدلة
                    var cmbMaterial = this.Controls.Find("cmbMaterial", true).FirstOrDefault() as DevExpress.XtraEditors.LookUpEdit;
                    if (cmbMaterial != null)
                    {
                        if (customsData.MaterialId > 0)
                            cmbMaterial.EditValue = customsData.MaterialId;
                        else
                            cmbMaterial.EditValue = null;
                    }

                    var cmbImporter = this.Controls.Find("cmbImporter", true).FirstOrDefault() as DevExpress.XtraEditors.LookUpEdit;
                    if (cmbImporter != null)
                    {
                        if (customsData.ImporterId > 0)
                            cmbImporter.EditValue = customsData.ImporterId;
                        else
                            cmbImporter.EditValue = null;
                    }

                    var cmbVessel = this.Controls.Find("cmbVessel", true).FirstOrDefault() as DevExpress.XtraEditors.LookUpEdit;
                    if (cmbVessel != null)
                    {
                        if (customsData.VesselId > 0)
                            cmbVessel.EditValue = customsData.VesselId;
                        else
                            cmbVessel.EditValue = null;
                    }

                    // عرض تاريخ الإنشاء
                    var lblCreatedAt = this.Controls.Find("lblCreatedAt", true).FirstOrDefault() as DevExpress.XtraEditors.LabelControl;
                    if (lblCreatedAt != null)
                    {
                        if (customsData.CustomsDataId > 0)
                        {
                            lblCreatedAt.Text = $"تاريخ الإنشاء: {customsData.CreatedAt:yyyy/MM/dd HH:mm}";
                            lblCreatedAt.Visible = true;
                        }
                        else
                        {
                            lblCreatedAt.Visible = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات إلى عناصر التحكم");
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is CustomsD customsData)
            {
                if (txtCustomsNumber != null)
                    customsData.CustomsNumber = txtCustomsNumber.Text.Trim();
                
                if (dateCustomsDate?.EditValue != null)
                    customsData.CustomsDate = DateOnly.FromDateTime((DateTime)dateCustomsDate.EditValue);
                
                if (cmbCustomsType != null)
                    customsData.CustomsType = cmbCustomsType.Text.Trim();
                
                if (txtCommonName != null)
                    customsData.CommonName = string.IsNullOrWhiteSpace(txtCommonName.Text) ? null : txtCommonName.Text.Trim();
                
                if (spinQuantity != null)
                    customsData.Quantity = spinQuantity.Value;

                // تعيين المعرفات من القوائم المنسدلة
                if (cmbMaterial?.EditValue != null)
                    customsData.MaterialId = (int)cmbMaterial.EditValue;

                if (cmbImporter?.EditValue != null)
                    customsData.ImporterId = (int)cmbImporter.EditValue;

                if (cmbVessel?.EditValue != null)
                    customsData.VesselId = (int)cmbVessel.EditValue;
                
                // تعيين تاريخ الإنشاء للبيانات الجديدة
                if (customsData.CustomsDataId == 0)
                {
                    customsData.CreatedAt = DateTime.Now;
                }
                else
                {
                    customsData.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من رقم الجمرك
            if (txtCustomsNumber == null || string.IsNullOrWhiteSpace(txtCustomsNumber.Text))
            {
                XtraMessageBox.Show("يرجى إدخال رقم الجمرك", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCustomsNumber?.Focus();
                return false;
            }

            // التحقق من تاريخ الجمرك
            if (dateCustomsDate?.EditValue == null)
            {
                XtraMessageBox.Show("يرجى إدخال تاريخ الجمرك", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dateCustomsDate?.Focus();
                return false;
            }

            // التحقق من نوع الجمرك
            if (cmbCustomsType == null || string.IsNullOrWhiteSpace(cmbCustomsType.Text))
            {
                XtraMessageBox.Show("يرجى اختيار نوع الجمرك", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCustomsType?.Focus();
                return false;
            }

            // التحقق من المادة
            if (cmbMaterial?.EditValue == null)
            {
                XtraMessageBox.Show("يرجى اختيار المادة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbMaterial?.Focus();
                return false;
            }

            // التحقق من المستورد
            if (cmbImporter?.EditValue == null)
            {
                XtraMessageBox.Show("يرجى اختيار المستورد", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbImporter?.Focus();
                return false;
            }

            // التحقق من الباخرة
            if (cmbVessel?.EditValue == null)
            {
                XtraMessageBox.Show("يرجى اختيار الباخرة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbVessel?.Focus();
                return false;
            }

            // التحقق من الكمية
            if (spinQuantity == null || spinQuantity.Value <= 0)
            {
                XtraMessageBox.Show("يرجى إدخال كمية صحيحة أكبر من صفر", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                spinQuantity?.Focus();
                return false;
            }

            return true;
        }

        /// <summary>
        /// حفظ الكائن في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            try
            {
                if (entity is CustomsD customsData && _context != null)
                {
                    // التحقق من تكرار رقم الجمرك
                    var customsNumber = txtCustomsNumber?.Text?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(customsNumber))
                    {
                        var existingCustomsData = await _context.CustomsDs
                            .Where(c => c.CustomsNumber == customsNumber && c.CustomsDataId != customsData.CustomsDataId)
                            .FirstOrDefaultAsync();

                        if (existingCustomsData != null)
                        {
                            XtraMessageBox.Show($"رقم الجمرك '{customsNumber}' موجود مسبقاً", "تكرار في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtCustomsNumber?.Focus();
                            return false;
                        }
                    }

                    if (customsData.CustomsDataId == 0)
                    {
                        customsData.CreatedAt = DateTime.Now;
                        _context.CustomsDs.Add(customsData);
                    }
                    else
                    {
                        customsData.ModifiedAt = DateTime.Now;
                        _context.CustomsDs.Update(customsData);
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حفظ البيان الجمركي");
                return false;
            }
        }

        /// <summary>
        /// حذف الكائن من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            try
            {
                if (entity is CustomsD customsData && customsData.CustomsDataId > 0 && _context != null)
                {
                    _context.CustomsDs.Remove(customsData);
                    await _context.SaveChangesAsync();
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف البيان الجمركي");
                return false;
            }
        }

        #endregion
    }
}
