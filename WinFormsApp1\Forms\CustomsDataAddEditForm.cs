﻿using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل البيانات الجمركية
    /// </summary>
    public partial class CustomsDataAddEditForm : XtraForm
    {
        #region Fields

        private WarehouseDbContext? _context;
        private CustomsD? _currentEntity;
        private bool _isEditMode;

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة بيان جمركي جديد
        /// </summary>
        public CustomsDataAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "📋 إدارة البيانات الجمركية - إضافة جديد";
                this.StartPosition = FormStartPosition.CenterParent;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;

                // تهيئة قاعدة البيانات
                _context = DatabaseService.GetDbContext();

                // إنشاء كائن جديد
                _currentEntity = new CustomsD
                {
                    CustomsDate = DateOnly.FromDateTime(DateTime.Now),
                    Quantity = 1,
                    CreatedAt = DateTime.Now
                };
                _isEditMode = false;

                // تحميل البيانات المرجعية
                LoadReferenceData();

                // تحميل البيانات إلى عناصر التحكم
                LoadEntityToControls();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// منشئ لتعديل بيان جمركي موجود
        /// </summary>
        public CustomsDataAddEditForm(CustomsD selectedCustomsData)
        {
            try
            {
                InitializeComponent();
                this.Text = "📋 إدارة البيانات الجمركية - تعديل";
                this.StartPosition = FormStartPosition.CenterParent;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;

                // تهيئة قاعدة البيانات
                _context = DatabaseService.GetDbContext();

                _currentEntity = selectedCustomsData;
                _isEditMode = true;

                // تحميل البيانات المرجعية
                LoadReferenceData();

                // تحميل البيانات إلى عناصر التحكم
                LoadEntityToControls();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Data Loading Methods

        /// <summary>
        /// تحميل البيانات المرجعية
        /// </summary>
        private void LoadReferenceData()
        {
            try
            {
                if (_context != null)
                {
                    // تحميل المواد
                    var materials = _context.Materials.OrderBy(m => m.MaterialName).ToList();
                    cmbMaterial.Properties.DataSource = materials;
                    cmbMaterial.Properties.DisplayMember = "MaterialName";
                    cmbMaterial.Properties.ValueMember = "MaterialId";

                    // تحميل المستوردين
                    var importers = _context.Importers.OrderBy(i => i.Name).ToList();
                    cmbImporter.Properties.DataSource = importers;
                    cmbImporter.Properties.DisplayMember = "Name";
                    cmbImporter.Properties.ValueMember = "ImporterId";

                    // تحميل البواخر
                    var vessels = _context.Vessels.OrderBy(v => v.VesselName).ToList();
                    cmbVessel.Properties.DataSource = vessels;
                    cmbVessel.Properties.DisplayMember = "VesselName";
                    cmbVessel.Properties.ValueMember = "VesselId";

                    // تحميل أنواع الجمرك
                    cmbCustomsType.Properties.Items.Clear();
                    cmbCustomsType.Properties.Items.AddRange(new string[] { "استيراد", "تصدير", "ترانزيت" });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات المرجعية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات إلى عناصر التحكم
        /// </summary>
        private void LoadEntityToControls()
        {
            try
            {
                if (_currentEntity != null)
                {
                    txtCustomsNumber.Text = _currentEntity.CustomsNumber ?? "";
                    dateCustomsDate.EditValue = _currentEntity.CustomsDate.ToDateTime(TimeOnly.MinValue);
                    cmbCustomsType.Text = _currentEntity.CustomsType ?? "";
                    txtCommonName.Text = _currentEntity.CommonName ?? "";
                    spinQuantity.Value = _currentEntity.Quantity;

                    // تعيين القيم للقوائم المنسدلة
                    if (_currentEntity.MaterialId > 0)
                        cmbMaterial.EditValue = _currentEntity.MaterialId;

                    if (_currentEntity.ImporterId > 0)
                        cmbImporter.EditValue = _currentEntity.ImporterId;

                    if (_currentEntity.VesselId > 0)
                        cmbVessel.EditValue = _currentEntity.VesselId;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Button Events

        /// <summary>
        /// حدث زر الحفظ
        /// </summary>
        private async void btnSave_Click(object sender, EventArgs e)
        {
            await SaveData();
        }

        /// <summary>
        /// حدث زر الإلغاء
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion

        #region Save and Validation Methods

        /// <summary>
        /// حفظ البيانات
        /// </summary>
        public async Task<bool> SaveData()
        {
            try
            {
                if (!ValidateData())
                    return false;

                LoadControlsToEntity();

                if (_context != null && _currentEntity != null)
                {
                    // التحقق من تكرار رقم الجمرك
                    var customsNumber = txtCustomsNumber.Text.Trim();

                    if (!string.IsNullOrEmpty(customsNumber))
                    {
                        var existingCustomsData = await _context.CustomsDs
                            .Where(c => c.CustomsNumber == customsNumber && c.CustomsDataId != _currentEntity.CustomsDataId)
                            .FirstOrDefaultAsync();

                        if (existingCustomsData != null)
                        {
                            XtraMessageBox.Show($"رقم الجمرك '{customsNumber}' موجود مسبقاً", "تكرار في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtCustomsNumber.Focus();
                            return false;
                        }
                    }

                    if (_currentEntity.CustomsDataId == 0)
                    {
                        _currentEntity.CreatedAt = DateTime.Now;
                        _context.CustomsDs.Add(_currentEntity);
                    }
                    else
                    {
                        _currentEntity.ModifiedAt = DateTime.Now;
                        _context.CustomsDs.Update(_currentEntity);
                    }

                    await _context.SaveChangesAsync();

                    XtraMessageBox.Show("تم حفظ البيانات بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    this.DialogResult = DialogResult.OK;
                    this.Close();

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                string errorMessage = $"خطأ في حفظ البيان الجمركي:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nتفاصيل الخطأ: {ex.InnerException.Message}";
                }
                XtraMessageBox.Show(errorMessage, "خطأ في الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateData()
        {
            try
            {
                // التحقق من رقم الجمرك
                if (string.IsNullOrWhiteSpace(txtCustomsNumber.Text))
                {
                    XtraMessageBox.Show("يرجى إدخال رقم الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCustomsNumber.Focus();
                    return false;
                }

                // التحقق من تاريخ الجمرك
                if (dateCustomsDate.EditValue == null)
                {
                    XtraMessageBox.Show("يرجى إدخال تاريخ الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dateCustomsDate.Focus();
                    return false;
                }

                // التحقق من نوع الجمرك
                if (string.IsNullOrWhiteSpace(cmbCustomsType.Text))
                {
                    XtraMessageBox.Show("يرجى اختيار نوع الجمرك", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbCustomsType.Focus();
                    return false;
                }

                // التحقق من المادة
                if (cmbMaterial.EditValue == null || !int.TryParse(cmbMaterial.EditValue.ToString(), out int materialId) || materialId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار المادة", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbMaterial.Focus();
                    return false;
                }

                // التحقق من المستورد
                if (cmbImporter.EditValue == null || !int.TryParse(cmbImporter.EditValue.ToString(), out int importerId) || importerId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار المستورد", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbImporter.Focus();
                    return false;
                }

                // التحقق من الباخرة
                if (cmbVessel.EditValue == null || !int.TryParse(cmbVessel.EditValue.ToString(), out int vesselId) || vesselId <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار الباخرة", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbVessel.Focus();
                    return false;
                }

                // التحقق من الكمية
                if (spinQuantity.Value <= 0)
                {
                    XtraMessageBox.Show("يرجى إدخال كمية صحيحة أكبر من صفر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    spinQuantity.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"خطأ في التحقق من البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        private void LoadControlsToEntity()
        {
            if (_currentEntity != null)
            {
                _currentEntity.CustomsNumber = txtCustomsNumber.Text.Trim();

                // التحقق من التاريخ
                if (dateCustomsDate.EditValue != null)
                {
                    _currentEntity.CustomsDate = DateOnly.FromDateTime((DateTime)dateCustomsDate.EditValue);
                }
                else
                {
                    _currentEntity.CustomsDate = DateOnly.FromDateTime(DateTime.Now);
                }

                _currentEntity.CustomsType = cmbCustomsType.Text.Trim();
                _currentEntity.CommonName = string.IsNullOrWhiteSpace(txtCommonName.Text) ? null : txtCommonName.Text.Trim();
                _currentEntity.Quantity = spinQuantity.Value;

                // تعيين المعرفات من القوائم المنسدلة مع التحقق
                if (cmbMaterial.EditValue != null && int.TryParse(cmbMaterial.EditValue.ToString(), out int materialId))
                {
                    _currentEntity.MaterialId = materialId;
                }

                if (cmbImporter.EditValue != null && int.TryParse(cmbImporter.EditValue.ToString(), out int importerId))
                {
                    _currentEntity.ImporterId = importerId;
                }

                if (cmbVessel.EditValue != null && int.TryParse(cmbVessel.EditValue.ToString(), out int vesselId))
                {
                    _currentEntity.VesselId = vesselId;
                }

                // تعيين تاريخ الإنشاء للبيانات الجديدة
                if (_currentEntity.CustomsDataId == 0)
                {
                    _currentEntity.CreatedAt = DateTime.Now;
                }
                else
                {
                    _currentEntity.ModifiedAt = DateTime.Now;
                }
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}