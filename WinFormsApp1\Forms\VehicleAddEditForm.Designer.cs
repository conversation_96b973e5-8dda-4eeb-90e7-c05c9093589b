namespace WinFormsApp1.Forms
{
    partial class VehicleAddEditForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            memoDescription = new DevExpress.XtraEditors.MemoEdit();
            cmbVehicleType = new DevExpress.XtraEditors.LookUpEdit();
            cmbTransportCompany = new DevExpress.XtraEditors.LookUpEdit();
            txtVehicleName = new DevExpress.XtraEditors.TextEdit();
            layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem2 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem3 = new DevExpress.XtraLayout.LayoutControlItem();
            layoutControlItem4 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)layoutControl1).BeginInit();
            layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)memoDescription.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbVehicleType.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)cmbTransportCompany.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)txtVehicleName.Properties).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlGroup1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem1).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem2).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem3).BeginInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem4).BeginInit();
            SuspendLayout();
            //
            // layoutControl1
            //
            layoutControl1.Controls.Add(memoDescription);
            layoutControl1.Controls.Add(cmbVehicleType);
            layoutControl1.Controls.Add(cmbTransportCompany);
            layoutControl1.Controls.Add(txtVehicleName);
            layoutControl1.Dock = DockStyle.Fill;
            layoutControl1.Location = new Point(0, 0);
            layoutControl1.Name = "layoutControl1";
            layoutControl1.OptionsView.RightToLeftMirroringApplied = true;
            layoutControl1.Root = layoutControlGroup1;
            layoutControl1.Size = new Size(800, 450);
            layoutControl1.TabIndex = 0;
            //
            // txtVehicleName
            //
            txtVehicleName.Location = new Point(12, 36);
            txtVehicleName.Name = "txtVehicleName";
            txtVehicleName.Properties.Appearance.Font = new Font("Tahoma", 10F);
            txtVehicleName.Properties.Appearance.Options.UseFont = true;
            txtVehicleName.Size = new Size(776, 26);
            txtVehicleName.StyleController = layoutControl1;
            txtVehicleName.TabIndex = 0;
            //
            // cmbTransportCompany
            //
            cmbTransportCompany.Location = new Point(12, 86);
            cmbTransportCompany.Name = "cmbTransportCompany";
            cmbTransportCompany.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbTransportCompany.Properties.Appearance.Options.UseFont = true;
            cmbTransportCompany.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbTransportCompany.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] { new DevExpress.XtraEditors.Controls.LookUpColumnInfo("Name", "اسم شركة النقل") });
            cmbTransportCompany.Properties.NullText = "اختر شركة النقل...";
            cmbTransportCompany.Size = new Size(776, 26);
            cmbTransportCompany.StyleController = layoutControl1;
            cmbTransportCompany.TabIndex = 1;
            //
            // cmbVehicleType
            //
            cmbVehicleType.Location = new Point(12, 136);
            cmbVehicleType.Name = "cmbVehicleType";
            cmbVehicleType.Properties.Appearance.Font = new Font("Tahoma", 10F);
            cmbVehicleType.Properties.Appearance.Options.UseFont = true;
            cmbVehicleType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] { new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo) });
            cmbVehicleType.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] { new DevExpress.XtraEditors.Controls.LookUpColumnInfo("VehicleTypeName", "نوع الآلية") });
            cmbVehicleType.Properties.NullText = "اختر نوع الآلية...";
            cmbVehicleType.Size = new Size(776, 26);
            cmbVehicleType.StyleController = layoutControl1;
            cmbVehicleType.TabIndex = 2;
            //
            // memoDescription
            //
            memoDescription.Location = new Point(12, 186);
            memoDescription.Name = "memoDescription";
            memoDescription.Properties.Appearance.Font = new Font("Tahoma", 10F);
            memoDescription.Properties.Appearance.Options.UseFont = true;
            memoDescription.Size = new Size(776, 252);
            memoDescription.StyleController = layoutControl1;
            memoDescription.TabIndex = 3;
            //
            // layoutControlGroup1
            //
            layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            layoutControlGroup1.GroupBordersVisible = false;
            layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] { layoutControlItem1, layoutControlItem2, layoutControlItem3, layoutControlItem4 });
            layoutControlGroup1.Name = "layoutControlGroup1";
            layoutControlGroup1.Size = new Size(800, 450);
            layoutControlGroup1.TextVisible = false;
            //
            // layoutControlItem1
            //
            layoutControlItem1.Control = txtVehicleName;
            layoutControlItem1.Location = new Point(0, 0);
            layoutControlItem1.Name = "layoutControlItem1";
            layoutControlItem1.Size = new Size(780, 50);
            layoutControlItem1.Text = "اسم آلية النقل *";
            layoutControlItem1.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem1.TextSize = new Size(89, 16);
            //
            // layoutControlItem2
            //
            layoutControlItem2.Control = cmbTransportCompany;
            layoutControlItem2.Location = new Point(0, 50);
            layoutControlItem2.Name = "layoutControlItem2";
            layoutControlItem2.Size = new Size(780, 50);
            layoutControlItem2.Text = "شركة النقل *";
            layoutControlItem2.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem2.TextSize = new Size(89, 16);
            //
            // layoutControlItem3
            //
            layoutControlItem3.Control = cmbVehicleType;
            layoutControlItem3.Location = new Point(0, 100);
            layoutControlItem3.Name = "layoutControlItem3";
            layoutControlItem3.Size = new Size(780, 50);
            layoutControlItem3.Text = "نوع الآلية *";
            layoutControlItem3.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem3.TextSize = new Size(89, 16);
            //
            // layoutControlItem4
            //
            layoutControlItem4.Control = memoDescription;
            layoutControlItem4.Location = new Point(0, 150);
            layoutControlItem4.Name = "layoutControlItem4";
            layoutControlItem4.Size = new Size(780, 280);
            layoutControlItem4.Text = "الوصف";
            layoutControlItem4.TextLocation = DevExpress.Utils.Locations.Top;
            layoutControlItem4.TextSize = new Size(89, 16);
            //
            // VehicleAddEditForm
            //
            AutoScaleDimensions = new SizeF(7F, 16F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(layoutControl1);
            Name = "VehicleAddEditForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "🚛 إدارة آليات النقل";
            ((System.ComponentModel.ISupportInitialize)layoutControl1).EndInit();
            layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)memoDescription.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbVehicleType.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)cmbTransportCompany.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)txtVehicleName.Properties).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlGroup1).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem1).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem2).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem3).EndInit();
            ((System.ComponentModel.ISupportInitialize)layoutControlItem4).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraEditors.MemoEdit memoDescription;
        private DevExpress.XtraEditors.LookUpEdit cmbVehicleType;
        private DevExpress.XtraEditors.LookUpEdit cmbTransportCompany;
        private DevExpress.XtraEditors.TextEdit txtVehicleName;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem2;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem3;
        private DevExpress.XtraLayout.LayoutControlItem layoutControlItem4;
    }
}