using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Grid;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Windows.Forms;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// فورم عرض جدول آليات النقل مع عمليات CRUD
    /// </summary>
    public partial class VehiclesForm : XtraForm
    {
        #region Fields

        private readonly WarehouseDbContext _context;
        private bool _isFormOpening = false; // لمنع فتح النافذة أكثر من مرة

        #endregion

        #region Constructor

        public VehiclesForm()
        {
            try
            {
                InitializeComponent();
                _context = DatabaseService.GetDbContext();
                SetupForm();
                LoadDataAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج آليات النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                message += $"\n\nStack Trace:\n{ex.StackTrace}";
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Setup Methods

        /// <summary>
        /// إعداد النموذج الأساسي
        /// </summary>
        private void SetupForm()
        {
            this.Text = "🚛 إدارة آليات النقل";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.WindowState = FormWindowState.Maximized;

            // إعداد نصوص الأزرار
            btnNew.Text = "🆕 جديد";
            btnView.Text = "👁️ عرض";
            btnPrint.Text = "🖨️ طباعة";
            btnExportToExcel.Text = "📊 تصدير Excel";
            btnRefresh.Text = "🔄 تحديث";

            // إعداد التلميحات
            btnNew.ToolTip = "إنشاء آلية نقل جديدة (Ctrl+N)";
            btnView.ToolTip = "عرض/تعديل آلية النقل المحددة (Enter)";
            btnPrint.ToolTip = "طباعة قائمة آليات النقل (Ctrl+P)";
            btnExportToExcel.ToolTip = "تصدير البيانات إلى Excel (Ctrl+E)";
            btnRefresh.ToolTip = "تحديث البيانات (F5)";

            // إعداد الجدول
            SetupGrid();

            // إعداد اختصارات لوحة المفاتيح
            this.KeyPreview = true;
            this.KeyDown += VehiclesForm_KeyDown;

            // إعداد حدث تغيير حجم النافذة
            this.Resize += VehiclesForm_Resize;
        }

        /// <summary>
        /// إعداد الجدول
        /// </summary>
        private void SetupGrid()
        {
            // إعداد العرض العام
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsView.ColumnAutoWidth = false; // إيقاف العرض التلقائي لتحكم أفضل
            gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            gridView1.OptionsSelection.EnableAppearanceFocusedRow = true;
            gridView1.OptionsSelection.MultiSelect = false;

            // إعدادات إضافية
            gridView1.OptionsView.BestFitMode = DevExpress.XtraGrid.Views.Grid.GridBestFitMode.Full;
            gridView1.OptionsView.BestFitMaxRowCount = 100;

            // إعداد الأعمدة
            ConfigureColumns();

            // إعداد أحداث الجدول
            gridView1.DoubleClick += GridView1_DoubleClick;
            gridView1.KeyDown += GridView1_KeyDown;
            gridView1.DataSourceChanged += GridView1_DataSourceChanged;

            // إعداد أحداث الأزرار
            btnNew.Click += btnNew_Click;
            btnView.Click += btnView_Click;
            btnRefresh.Click += btnRefresh_Click;
            btnPrint.Click += btnPrint_Click;
            btnExportToExcel.Click += btnExportToExcel_Click;

            // إعداد حدث البحث
            txtSearch.EditValueChanged += txtSearch_EditValueChanged;
        }

        /// <summary>
        /// إعداد أعمدة الجدول
        /// </summary>
        private void ConfigureColumns()
        {
            // إخفاء جميع الأعمدة أولاً
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridView1.Columns)
            {
                col.Visible = false;
            }

            // إعداد الأعمدة المطلوبة
            if (gridView1.Columns["VehicleId"] != null)
            {
                gridView1.Columns["VehicleId"].Caption = "المعرف";
                gridView1.Columns["VehicleId"].Visible = true;
                gridView1.Columns["VehicleId"].VisibleIndex = 0;
                gridView1.Columns["VehicleId"].Width = 80;
            }

            if (gridView1.Columns["VehicleName"] != null)
            {
                gridView1.Columns["VehicleName"].Caption = "اسم آلية النقل";
                gridView1.Columns["VehicleName"].Visible = true;
                gridView1.Columns["VehicleName"].VisibleIndex = 1;
                gridView1.Columns["VehicleName"].Width = 200;
            }

            if (gridView1.Columns["TransportCompany.Name"] != null)
            {
                gridView1.Columns["TransportCompany.Name"].Caption = "شركة النقل";
                gridView1.Columns["TransportCompany.Name"].Visible = true;
                gridView1.Columns["TransportCompany.Name"].VisibleIndex = 2;
                gridView1.Columns["TransportCompany.Name"].Width = 150;
            }

            if (gridView1.Columns["VehicleType.VehicleTypeName"] != null)
            {
                gridView1.Columns["VehicleType.VehicleTypeName"].Caption = "نوع الآلية";
                gridView1.Columns["VehicleType.VehicleTypeName"].Visible = true;
                gridView1.Columns["VehicleType.VehicleTypeName"].VisibleIndex = 3;
                gridView1.Columns["VehicleType.VehicleTypeName"].Width = 120;
            }

            if (gridView1.Columns["Description"] != null)
            {
                gridView1.Columns["Description"].Caption = "الوصف";
                gridView1.Columns["Description"].Visible = true;
                gridView1.Columns["Description"].VisibleIndex = 4;
                gridView1.Columns["Description"].Width = 250;
            }

            if (gridView1.Columns["CreatedAt"] != null)
            {
                gridView1.Columns["CreatedAt"].Caption = "تاريخ الإنشاء";
                gridView1.Columns["CreatedAt"].Visible = true;
                gridView1.Columns["CreatedAt"].VisibleIndex = 5;
                gridView1.Columns["CreatedAt"].Width = 120;
                gridView1.Columns["CreatedAt"].DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                gridView1.Columns["CreatedAt"].DisplayFormat.FormatString = "dd/MM/yyyy";
            }
        }

        #endregion

        #region Form Events

        /// <summary>
        /// معالج حدث تغيير حجم النافذة
        /// </summary>
        private void VehiclesForm_Resize(object sender, EventArgs e)
        {
            // إعادة تعديل عرض الأعمدة عند تغيير حجم النافذة
            if (gridView1 != null && this.WindowState != FormWindowState.Minimized && gridControl1.Width > 0)
            {
                // تأخير قصير لضمان اكتمال تغيير الحجم
                System.Windows.Forms.Timer timer = new System.Windows.Forms.Timer();
                timer.Interval = 100;
                timer.Tick += (s, args) =>
                {
                    timer.Stop();
                    timer.Dispose();
                    OptimizeColumnWidths();
                };
                timer.Start();
            }
        }

        #endregion

        #region Keyboard Events

        /// <summary>
        /// أحداث لوحة المفاتيح للنموذج
        /// </summary>
        private void VehiclesForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control)
            {
                switch (e.KeyCode)
                {
                    case Keys.N:
                        if (!_isFormOpening)
                        {
                            btnNew_Click(null, null);
                            e.Handled = true;
                        }
                        break;
                    case Keys.P:
                        btnPrint_Click(null, null);
                        e.Handled = true;
                        break;
                    case Keys.E:
                        btnExportToExcel_Click(null, null);
                        e.Handled = true;
                        break;
                }
            }
            else
            {
                switch (e.KeyCode)
                {
                    case Keys.F5:
                        btnRefresh_Click(null, null);
                        e.Handled = true;
                        break;
                    case Keys.Enter:
                        if (!_isFormOpening)
                        {
                            btnView_Click(null, null);
                            e.Handled = true;
                        }
                        break;
                    case Keys.Delete:
                        // يمكن إضافة وظيفة الحذف هنا لاحقاً
                        break;
                }
            }
        }

        #endregion

        #region Data Management

        /// <summary>
        /// تحميل بيانات آليات النقل
        /// </summary>
        private async void LoadDataAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                gridControl1.Enabled = false;

                // تشخيص: فحص الجداول
                var vehiclesCount = await _context.Vehicles.CountAsync();
                var companiesCount = await _context.TransportCompanies.CountAsync();
                var vehicleTypesCount = await _context.VehicleTypes.CountAsync();

                MessageBox.Show($"عدد آليات النقل: {vehiclesCount}\nعدد شركات النقل: {companiesCount}\nعدد أنواع الآليات: {vehicleTypesCount}",
                    "تشخيص قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);

                var query = _context.Vehicles
                    .Include(v => v.TransportCompany)
                    .Include(v => v.VehicleType)
                    .Include(v => v.CreatedByUser)
                    .Include(v => v.ModifiedByUser)
                    .AsQueryable();

                // تطبيق البحث إذا كان هناك نص بحث
                var searchText = txtSearch.Text?.Trim();
                if (!string.IsNullOrEmpty(searchText))
                {
                    query = query.Where(v =>
                        v.VehicleName.Contains(searchText) ||
                        v.TransportCompany.Name.Contains(searchText) ||
                        v.VehicleType.VehicleTypeName.Contains(searchText) ||
                        (v.Description != null && v.Description.Contains(searchText)));
                }

                var vehicles = await query
                    .OrderBy(v => v.VehicleName)
                    .ToListAsync();

                // تشخيص: عرض عدد السجلات المحملة
                MessageBox.Show($"تم تحميل {vehicles.Count} سجل من قاعدة البيانات", "تشخيص", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تحقق من وجود البيانات
                if (vehicles == null || vehicles.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات، سيتم إنشاء بيانات تجريبية", "تشخيص", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    // إنشاء بيانات تجريبية إذا لم توجد
                    await CreateSampleDataIfNeeded();
                    vehicles = await query.OrderBy(v => v.VehicleName).ToListAsync();
                    MessageBox.Show($"بعد إنشاء البيانات التجريبية: {vehicles.Count} سجل", "تشخيص", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                gridControl1.DataSource = vehicles;
                MessageBox.Show($"تم تعيين DataSource بـ {vehicles.Count} سجل", "تشخيص", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إعداد الأعمدة بعد تحميل البيانات
                ConfigureColumns();
                MessageBox.Show("تم إعداد الأعمدة", "تشخيص", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // تحديث عدد السجلات
                UpdateRecordCount(vehicles.Count);
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات");
            }
            finally
            {
                gridControl1.Enabled = true;
            }
        }

        /// <summary>
        /// إنشاء بيانات تجريبية إذا لم توجد
        /// </summary>
        private async Task CreateSampleDataIfNeeded()
        {
            try
            {
                // التحقق من وجود شركات نقل
                var transportCompaniesCount = await _context.TransportCompanies.CountAsync();
                if (transportCompaniesCount == 0)
                {
                    // إنشاء شركات نقل تجريبية
                    var companies = new[]
                    {
                        new TransportCompany { Name = "شركة النقل السريع", ContactDetails = "*********", CreatedAt = DateTime.Now },
                        new TransportCompany { Name = "شركة الشحن المتقدم", ContactDetails = "*********", CreatedAt = DateTime.Now }
                    };
                    _context.TransportCompanies.AddRange(companies);
                    await _context.SaveChangesAsync();
                }

                // التحقق من وجود أنواع آليات
                var vehicleTypesCount = await _context.VehicleTypes.CountAsync();
                if (vehicleTypesCount == 0)
                {
                    // إنشاء أنواع آليات تجريبية
                    var vehicleTypes = new[]
                    {
                        new VehicleType { VehicleTypeName = "شاحنة كبيرة" },
                        new VehicleType { VehicleTypeName = "شاحنة متوسطة" },
                        new VehicleType { VehicleTypeName = "حاوية" }
                    };
                    _context.VehicleTypes.AddRange(vehicleTypes);
                    await _context.SaveChangesAsync();
                }

                // التحقق من وجود آليات نقل
                var vehiclesCount = await _context.Vehicles.CountAsync();
                if (vehiclesCount == 0)
                {
                    var firstCompany = await _context.TransportCompanies.FirstAsync();
                    var firstVehicleType = await _context.VehicleTypes.FirstAsync();

                    // إنشاء آليات نقل تجريبية
                    var vehicles = new[]
                    {
                        new Vehicle
                        {
                            VehicleName = "شاحنة رقم 001",
                            TransportCompanyId = firstCompany.TransportCompanyId,
                            VehicleTypeId = firstVehicleType.VehicleTypeId,
                            Description = "شاحنة نقل بضائع ثقيلة",
                            CreatedAt = DateTime.Now
                        },
                        new Vehicle
                        {
                            VehicleName = "شاحنة رقم 002",
                            TransportCompanyId = firstCompany.TransportCompanyId,
                            VehicleTypeId = firstVehicleType.VehicleTypeId,
                            Description = "شاحنة نقل بضائع متوسطة",
                            CreatedAt = DateTime.Now
                        }
                    };
                    _context.Vehicles.AddRange(vehicles);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء البيانات التجريبية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// تحديث عدد السجلات والإحصائيات
        /// </summary>
        private void UpdateRecordCount(int count)
        {
            try
            {
                // تحديث النص في العنوان ليشمل العدد
                this.Text = $"🚛 إدارة آليات النقل - إجمالي: {count}";

                // تحديث العنوان في الشاشة أيضاً
                if (labelControl1 != null)
                {
                    labelControl1.Text = $"🚛 إدارة آليات النقل - إجمالي الآليات: {count}";
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحديث العدد
            }
        }

        /// <summary>
        /// معالجة الأخطاء
        /// </summary>
        private void HandleError(Exception ex, string operation)
        {
            string message = $"خطأ في {operation}:\n{ex.Message}";
            if (ex.InnerException != null)
            {
                message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
            }
            XtraMessageBox.Show(message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        #endregion

        #region Button Events

        /// <summary>
        /// إنشاء آلية نقل جديدة
        /// </summary>
        private void btnNew_Click(object sender, EventArgs e)
        {
            // منع فتح النافذة إذا كانت مفتوحة بالفعل
            if (_isFormOpening) return;

            try
            {
                _isFormOpening = true; // تعيين العلامة لمنع الفتح المتكرر

                var form = new VehicleAddEditForm();
                var result = form.ShowDialog();

                if (result == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                string message = $"خطأ في فتح نموذج إضافة آلية نقل جديدة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nتفاصيل إضافية:\n{ex.InnerException.Message}";
                }
                message += $"\n\nStack Trace:\n{ex.StackTrace}";
                MessageBox.Show(message, "خطأ في فتح النموذج", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isFormOpening = false; // إعادة تعيين العلامة
            }
        }

        /// <summary>
        /// عرض/تعديل آلية النقل المحددة
        /// </summary>
        private void btnView_Click(object sender, EventArgs e)
        {
            OpenSelectedVehicle();
        }

        /// <summary>
        /// فتح آلية النقل المحددة للتعديل
        /// </summary>
        private void OpenSelectedVehicle()
        {
            // منع التنفيذ إذا كانت نافذة مفتوحة بالفعل
            if (_isFormOpening) return;

            try
            {
                var selectedVehicle = gridView1.GetFocusedRow() as Vehicle;
                if (selectedVehicle == null)
                {
                    XtraMessageBox.Show("يرجى اختيار آلية نقل للعرض", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                _isFormOpening = true; // تعيين العلامة لمنع الفتح المتكرر

                var form = new VehicleAddEditForm(selectedVehicle);
                var result = form.ShowDialog();

                if (result == DialogResult.OK)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "فتح نموذج تعديل آلية النقل");
            }
            finally
            {
                _isFormOpening = false; // إعادة تعيين العلامة
            }
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadDataAsync();
                XtraMessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحديث البيانات");
            }
        }

        /// <summary>
        /// طباعة البيانات
        /// </summary>
        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                gridControl1.ShowPrintPreview();
            }
            catch (Exception ex)
            {
                HandleError(ex, "طباعة البيانات");
            }
        }

        /// <summary>
        /// تصدير البيانات إلى Excel
        /// </summary>
        private void btnExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "Excel Files|*.xlsx";
                    saveDialog.Title = "حفظ ملف Excel";
                    saveDialog.FileName = $"آليات_النقل_{DateTime.Now:yyyy-MM-dd}";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        gridView1.ExportToXlsx(saveDialog.FileName);
                        XtraMessageBox.Show("تم تصدير البيانات بنجاح", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تصدير البيانات");
            }
        }

        #endregion

        #region Search Events

        private void txtSearch_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (!_isFormOpening)
                {
                    LoadDataAsync();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء البحث المؤقتة
            }
        }

        #endregion

        #region Grid Events

        private async void GridView1_DoubleClick(object sender, EventArgs e)
        {
            btnView_Click(sender, new EventArgs());
        }

        private async void GridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                btnView_Click(sender, new EventArgs());
            }
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            // تحديث عدد السجلات أو أي معلومات أخرى
        }

        #endregion

        #region Data Methods





        /// <summary>
        /// الحصول على آلية النقل المحددة
        /// </summary>
        private Vehicle GetSelectedVehicle()
        {
            try
            {
                var selectedRowHandle = gridView1.FocusedRowHandle;
                if (selectedRowHandle >= 0)
                {
                    return gridView1.GetRow(selectedRowHandle) as Vehicle;
                }
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// تحسين عرض الأعمدة
        /// </summary>
        private void OptimizeColumnWidths()
        {
            try
            {
                if (gridView1 != null && gridControl1.Width > 0)
                {
                    gridView1.BestFitColumns();
                }
            }
            catch (Exception ex)
            {
                // تجاهل أخطاء تحسين العرض
            }
        }

        #endregion

        #region Dispose

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _context?.Dispose();
                components?.Dispose();
            }
            base.Dispose(disposing);
        }

        #endregion
    }
}
