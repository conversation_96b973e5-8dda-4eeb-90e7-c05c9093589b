using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل البواخر
    /// </summary>
    public partial class VesselAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة باخرة جديدة
        /// </summary>
        public VesselAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🚢 إدارة البواخر";

                _currentEntity = new Vessel();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة الباخرة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل باخرة موجودة
        /// </summary>
        public VesselAddEditForm(Vessel selectedVessel)
        {
            try
            {
                InitializeComponent();
                this.Text = "🚢 إدارة البواخر";

                _currentEntity = selectedVessel;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل الباخرة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var vessels = await _context!.Vessels
                    .Include(v => v.CreatedByUser)
                    .Include(v => v.ModifiedByUser)
                    .OrderBy(v => v.VesselName)
                    .ToListAsync();

                return vessels.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البواخر من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Vessel
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Vessel vessel)
            {
                txtVesselName.Text = vessel.VesselName ?? "";
                txtNationality.Text = vessel.Nationality ?? "";
                memoDescription.Text = vessel.Description ?? "";
                
                // عرض تاريخ الإنشاء
                if (vessel.VesselId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {vessel.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Vessel vessel)
            {
                vessel.VesselName = txtVesselName.Text.Trim();
                vessel.Nationality = txtNationality.Text.Trim();
                vessel.Description = string.IsNullOrWhiteSpace(memoDescription.Text) ? null : memoDescription.Text.Trim();
                
                // تعيين تاريخ الإنشاء للبواخر الجديدة
                if (vessel.VesselId == 0)
                {
                    vessel.CreatedAt = DateTime.Now;
                }
                else
                {
                    vessel.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم الباخرة
            if (string.IsNullOrWhiteSpace(txtVesselName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم الباخرة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtVesselName.Focus();
                return false;
            }

            // التحقق من طول اسم الباخرة
            if (txtVesselName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم الباخرة لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtVesselName.Focus();
                return false;
            }

            // التحقق من الجنسية
            if (string.IsNullOrWhiteSpace(txtNationality.Text))
            {
                XtraMessageBox.Show("يرجى إدخال جنسية الباخرة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNationality.Focus();
                return false;
            }

            // التحقق من طول الجنسية
            if (txtNationality.Text.Trim().Length > 100)
            {
                XtraMessageBox.Show("جنسية الباخرة لا يجب أن تتجاوز 100 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNationality.Focus();
                return false;
            }

            // التحقق من طول الوصف
            if (!string.IsNullOrWhiteSpace(memoDescription.Text) && memoDescription.Text.Trim().Length > 500)
            {
                XtraMessageBox.Show("الوصف لا يجب أن يتجاوز 500 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                memoDescription.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم الباخرة
            if (_currentEntity is Vessel currentVessel)
            {
                var existingVessel = _context!.Vessels.FirstOrDefault(v => 
                    v.VesselName == txtVesselName.Text.Trim() && v.VesselId != currentVessel.VesselId);
                
                if (existingVessel != null)
                {
                    XtraMessageBox.Show("اسم الباخرة موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtVesselName.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// حفظ الباخرة في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Vessel vessel)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        vessel.CreatedAt = DateTime.Now;
                        vessel.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Vessels.Add(vessel);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingVessel = await _context!.Vessels.FindAsync(vessel.VesselId);
                        if (existingVessel != null)
                        {
                            existingVessel.VesselName = vessel.VesselName;
                            existingVessel.Nationality = vessel.Nationality;
                            existingVessel.Description = vessel.Description;
                            existingVessel.ModifiedAt = DateTime.Now;
                            existingVessel.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Vessels.Update(existingVessel);
                        }
                        else
                        {
                            return false; // الباخرة غير موجودة
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ الباخرة");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف الباخرة من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Vessel vessel)
            {
                try
                {
                    // البحث عن الباخرة في قاعدة البيانات للتأكد من وجودها
                    var existingVessel = await _context!.Vessels.FindAsync(vessel.VesselId);
                    if (existingVessel != null)
                    {
                        _context.Vessels.Remove(existingVessel);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("الباخرة غير موجودة"), "حذف الباخرة");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف الباخرة");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Vessel vessel)
            {
                return $"هل أنت متأكد من حذف الباخرة '{vessel.VesselName}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذه الباخرة؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            return isEdit ? "تم تحديث بيانات الباخرة بنجاح" : "تم إضافة الباخرة الجديدة بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Vessel vessel)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "🚢 إضافة باخرة جديدة",
                    FormMode.Edit => $"🚢 تعديل الباخرة: {vessel.VesselName}",
                    FormMode.View => $"🚢 عرض الباخرة: {vessel.VesselName}",
                    _ => "🚢 إدارة البواخر"
                };
            }
            else
            {
                this.Text = "🚢 إدارة البواخر";
            }
        }

        #endregion
    }
}
