using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل المواد
    /// </summary>
    public partial class MaterialAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة مادة جديدة
        /// </summary>
        public MaterialAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "📦 إدارة المواد";

                _currentEntity = new Material();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة المادة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل مادة موجودة
        /// </summary>
        public MaterialAddEditForm(Material selectedMaterial)
        {
            try
            {
                InitializeComponent();
                this.Text = "📦 إدارة المواد";

                _currentEntity = selectedMaterial;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                _ = LoadDataListAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل المادة:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                // البقاء في وضع الإضافة مع سجل فارغ
                _currentIndex = -1; // لا نحدد أي سجل
                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var materials = await _context!.Materials
                    .Include(m => m.CreatedByUser)
                    .Include(m => m.ModifiedByUser)
                    .OrderBy(m => m.MaterialName)
                    .ToListAsync();

                return materials.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل المواد من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new Material
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل بيانات الكائن إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            if (_currentEntity is Material material)
            {
                txtMaterialName.Text = material.MaterialName ?? "";
                txtUnitType.Text = material.UnitType ?? "";
                memoDescription.Text = material.Description ?? "";
                
                // عرض تاريخ الإنشاء
                if (material.MaterialId > 0)
                {
                    lblCreatedAt.Text = $"تاريخ الإنشاء: {material.CreatedAt:yyyy/MM/dd HH:mm}";
                    lblCreatedAt.Visible = true;
                }
                else
                {
                    lblCreatedAt.Visible = false;
                }
            }
        }

        /// <summary>
        /// تحميل بيانات عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            if (_currentEntity is Material material)
            {
                material.MaterialName = txtMaterialName.Text.Trim();
                material.UnitType = txtUnitType.Text.Trim();
                material.Description = string.IsNullOrWhiteSpace(memoDescription.Text) ? null : memoDescription.Text.Trim();
                
                // تعيين تاريخ الإنشاء للمواد الجديدة
                if (material.MaterialId == 0)
                {
                    material.CreatedAt = DateTime.Now;
                }
                else
                {
                    material.ModifiedAt = DateTime.Now;
                }
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            // التحقق من اسم المادة
            if (string.IsNullOrWhiteSpace(txtMaterialName.Text))
            {
                XtraMessageBox.Show("يرجى إدخال اسم المادة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return false;
            }

            // التحقق من طول اسم المادة
            if (txtMaterialName.Text.Trim().Length > 200)
            {
                XtraMessageBox.Show("اسم المادة لا يجب أن يتجاوز 200 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtMaterialName.Focus();
                return false;
            }

            // التحقق من نوع الوحدة
            if (string.IsNullOrWhiteSpace(txtUnitType.Text))
            {
                XtraMessageBox.Show("يرجى إدخال نوع الوحدة", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUnitType.Focus();
                return false;
            }

            // التحقق من طول نوع الوحدة
            if (txtUnitType.Text.Trim().Length > 50)
            {
                XtraMessageBox.Show("نوع الوحدة لا يجب أن يتجاوز 50 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUnitType.Focus();
                return false;
            }

            // التحقق من طول الوصف
            if (!string.IsNullOrWhiteSpace(memoDescription.Text) && memoDescription.Text.Trim().Length > 500)
            {
                XtraMessageBox.Show("الوصف لا يجب أن يتجاوز 500 حرف", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                memoDescription.Focus();
                return false;
            }

            // التحقق من عدم تكرار اسم المادة
            if (_currentEntity is Material currentMaterial)
            {
                var existingMaterial = _context!.Materials.FirstOrDefault(m => 
                    m.MaterialName == txtMaterialName.Text.Trim() && m.MaterialId != currentMaterial.MaterialId);
                
                if (existingMaterial != null)
                {
                    XtraMessageBox.Show("اسم المادة موجود بالفعل، يرجى اختيار اسم آخر", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtMaterialName.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// حفظ المادة في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            if (entity is Material material)
            {
                try
                {
                    if (CurrentMode == FormMode.Add)
                    {
                        material.CreatedAt = DateTime.Now;
                        material.CreatedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                        _context!.Materials.Add(material);
                    }
                    else
                    {
                        // للتعديل، نحتاج للتأكد من أن Entity Framework يتتبع التغييرات
                        var existingMaterial = await _context!.Materials.FindAsync(material.MaterialId);
                        if (existingMaterial != null)
                        {
                            existingMaterial.MaterialName = material.MaterialName;
                            existingMaterial.UnitType = material.UnitType;
                            existingMaterial.Description = material.Description;
                            existingMaterial.ModifiedAt = DateTime.Now;
                            existingMaterial.ModifiedByUserId = 1; // مؤقتاً حتى نضيف نظام المستخدمين
                            _context.Materials.Update(existingMaterial);
                        }
                        else
                        {
                            return false; // المادة غير موجودة
                        }
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حفظ المادة");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// حذف المادة من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            if (entity is Material material)
            {
                try
                {
                    // البحث عن المادة في قاعدة البيانات للتأكد من وجودها
                    var existingMaterial = await _context!.Materials.FindAsync(material.MaterialId);
                    if (existingMaterial != null)
                    {
                        _context.Materials.Remove(existingMaterial);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                    else
                    {
                        HandleError(new Exception("المادة غير موجودة"), "حذف المادة");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    HandleError(ex, "حذف المادة");
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// رسالة تأكيد الحذف
        /// </summary>
        protected override string GetDeleteConfirmationMessage()
        {
            if (_currentEntity is Material material)
            {
                return $"هل أنت متأكد من حذف المادة '{material.MaterialName}'؟\nهذا الإجراء لا يمكن التراجع عنه!";
            }
            return "هل أنت متأكد من حذف هذه المادة؟";
        }

        /// <summary>
        /// رسالة نجاح الحفظ
        /// </summary>
        protected override string GetSaveSuccessMessage(bool isEdit)
        {
            if (_currentEntity is Material material)
            {
                return isEdit ?
                    $"تم تحديث المادة '{material.MaterialName}' بنجاح" :
                    $"تم إنشاء المادة '{material.MaterialName}' بنجاح";
            }
            return isEdit ? "تم تحديث المادة بنجاح" : "تم إنشاء المادة بنجاح";
        }

        /// <summary>
        /// تحديث عنوان النموذج
        /// </summary>
        protected override void UpdateFormTitle()
        {
            if (_currentEntity is Material material)
            {
                this.Text = CurrentMode switch
                {
                    FormMode.Add => "📦 إضافة مادة جديدة",
                    FormMode.Edit => $"📦 تعديل المادة: {material.MaterialName}",
                    FormMode.View => $"📦 عرض المادة: {material.MaterialName}",
                    _ => "📦 إدارة المواد"
                };
            }
            else
            {
                this.Text = "📦 إدارة المواد";
            }
        }

        #endregion
    }
}
