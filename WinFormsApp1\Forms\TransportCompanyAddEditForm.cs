using DevExpress.XtraEditors;
using WinFormsApp1.Data;
using WinFormsApp1.Models;
using WinFormsApp1.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;

namespace WinFormsApp1.Forms
{
    /// <summary>
    /// نموذج إضافة وتعديل شركات النقل
    /// </summary>
    public partial class TransportCompanyAddEditForm : MasterF
    {
        #region Fields

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ لإضافة شركة نقل جديدة
        /// </summary>
        public TransportCompanyAddEditForm()
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = new TransportCompany();
                SetMode(FormMode.Add);
                LoadEntityToControls();
                // تحميل البيانات للتنقل لكن البقاء في وضع الإضافة
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج إضافة شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// منشئ لتعديل شركة نقل موجودة
        /// </summary>
        public TransportCompanyAddEditForm(TransportCompany selectedTransportCompany)
        {
            try
            {
                InitializeComponent();
                this.Text = "🚛 إدارة شركات النقل";

                _currentEntity = selectedTransportCompany;
                SetMode(FormMode.Edit);
                LoadEntityToControls();
                // تحميل البيانات للتنقل
                _ = LoadDataForNavigationAsync();
            }
            catch (Exception ex)
            {
                string message = $"خطأ في تهيئة نموذج تعديل شركة النقل:\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    message += $"\n\nالسبب: {ex.InnerException.Message}";
                }
                MessageBox.Show(message, "خطأ في التهيئة", MessageBoxButtons.OK, MessageBoxIcon.Error);
                throw;
            }
        }

        /// <summary>
        /// تحديد الفهرس الحالي بعد تحميل البيانات
        /// </summary>
        private void SetCurrentIndexAfterDataLoad()
        {
            if (_currentEntity is TransportCompany currentTransportCompany && _dataList != null)
            {
                _currentIndex = _dataList.FindIndex(item =>
                    item is TransportCompany tc && tc.TransportCompanyId == currentTransportCompany.TransportCompanyId);

                if (_currentIndex == -1 && _dataList.Count > 0)
                {
                    _currentIndex = 0;
                }

                UpdateNavigationInfo();
                UpdateButtonStates();
            }
        }

        #endregion

        #region Override Methods

        /// <summary>
        /// إنشاء كائن جديد
        /// </summary>
        protected override object CreateNewEntityInstance()
        {
            return new TransportCompany
            {
                CreatedAt = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل البيانات للتنقل فقط دون تغيير الوضع الحالي
        /// </summary>
        private async Task LoadDataForNavigationAsync()
        {
            try
            {
                // تحميل البيانات للتنقل
                await LoadDataListAsync();

                if (CurrentMode == FormMode.Add)
                {
                    // البقاء في وضع الإضافة مع سجل فارغ
                    _currentIndex = -1; // لا نحدد أي سجل
                }
                else
                {
                    // في وضع التعديل، تحديد الفهرس الصحيح
                    SetCurrentIndexAfterDataLoad();
                }

                UpdateNavigationInfo();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات للتنقل");
            }
        }

        /// <summary>
        /// تحميل البيانات المرجعية بشكل آمن
        /// </summary>
        private void LoadReferenceDataSafe()
        {
            try
            {
                if (_context == null) return;

                // لا توجد بيانات مرجعية إضافية لشركات النقل
                // يمكن إضافة أي بيانات مرجعية هنا لاحقاً
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات المرجعية");
            }
        }

        /// <summary>
        /// تحميل الكائنات من قاعدة البيانات
        /// </summary>
        protected override async Task<List<object>> LoadEntitiesFromDatabase()
        {
            try
            {
                var transportCompanies = await _context!.TransportCompanies
                    .Include(tc => tc.CreatedByUser)
                    .Include(tc => tc.ModifiedByUser)
                    .OrderBy(tc => tc.Name)
                    .ToListAsync();

                return transportCompanies.Cast<object>().ToList();
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل شركات النقل من قاعدة البيانات");
                return new List<object>();
            }
        }

        /// <summary>
        /// تحميل البيانات إلى عناصر التحكم
        /// </summary>
        protected override void LoadEntityToControls()
        {
            try
            {
                if (_currentEntity is TransportCompany transportCompany)
                {
                    txtName.Text = transportCompany.Name ?? "";
                    memoContactDetails.Text = transportCompany.ContactDetails ?? "";
                    
                    // عرض معلومات الإنشاء والتعديل
                    if (transportCompany.TransportCompanyId > 0)
                    {
                        lblCreatedAt.Text = $"تاريخ الإنشاء: {transportCompany.CreatedAt:dd/MM/yyyy HH:mm}";
                        if (transportCompany.ModifiedAt.HasValue)
                        {
                            lblModifiedAt.Text = $"تاريخ التعديل: {transportCompany.ModifiedAt:dd/MM/yyyy HH:mm}";
                            lblModifiedAt.Visible = true;
                        }
                        else
                        {
                            lblModifiedAt.Visible = false;
                        }
                    }
                    else
                    {
                        lblCreatedAt.Text = "";
                        lblModifiedAt.Visible = false;
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات إلى عناصر التحكم");
            }
        }

        /// <summary>
        /// تحميل البيانات من عناصر التحكم إلى الكائن
        /// </summary>
        protected override void LoadControlsToEntity()
        {
            try
            {
                if (_currentEntity is TransportCompany transportCompany)
                {
                    transportCompany.Name = string.IsNullOrWhiteSpace(txtName.Text) ? null : txtName.Text.Trim();
                    transportCompany.ContactDetails = string.IsNullOrWhiteSpace(memoContactDetails.Text) ? null : memoContactDetails.Text.Trim();

                    // تعيين تاريخ الإنشاء للكيانات الجديدة
                    if (transportCompany.TransportCompanyId == 0)
                    {
                        transportCompany.CreatedAt = DateTime.Now;
                    }
                    else
                    {
                        transportCompany.ModifiedAt = DateTime.Now;
                    }
                }
            }
            catch (Exception ex)
            {
                HandleError(ex, "تحميل البيانات من عناصر التحكم");
            }
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        protected override bool ValidateEntityData()
        {
            try
            {
                // التحقق من اسم شركة النقل
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    XtraMessageBox.Show("يرجى إدخال اسم شركة النقل", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول اسم شركة النقل
                if (txtName.Text.Trim().Length > 100)
                {
                    XtraMessageBox.Show("اسم شركة النقل يجب أن يكون أقل من 100 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                // التحقق من طول تفاصيل الاتصال
                if (!string.IsNullOrWhiteSpace(memoContactDetails.Text) && memoContactDetails.Text.Trim().Length > 500)
                {
                    XtraMessageBox.Show("تفاصيل الاتصال يجب أن تكون أقل من 500 حرف", "خطأ في البيانات",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    memoContactDetails.Focus();
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                HandleError(ex, "التحقق من صحة البيانات");
                return false;
            }
        }

        /// <summary>
        /// حفظ الكائن في قاعدة البيانات
        /// </summary>
        protected override async Task<bool> SaveEntityToDatabase(object entity)
        {
            try
            {
                if (entity is TransportCompany transportCompany)
                {
                    // التحقق من تكرار اسم شركة النقل
                    var transportCompanyName = transportCompany.Name?.Trim();

                    if (!string.IsNullOrEmpty(transportCompanyName))
                    {
                        var existingTransportCompany = await _context!.TransportCompanies
                            .Where(tc => tc.Name == transportCompanyName && tc.TransportCompanyId != transportCompany.TransportCompanyId)
                            .FirstOrDefaultAsync();

                        if (existingTransportCompany != null)
                        {
                            XtraMessageBox.Show($"شركة النقل '{transportCompanyName}' موجودة مسبقاً", "تكرار في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtName.Focus();
                            return false;
                        }
                    }

                    if (transportCompany.TransportCompanyId == 0)
                    {
                        // إضافة شركة نقل جديدة
                        _context!.TransportCompanies.Add(transportCompany);
                    }
                    else
                    {
                        // تحديث شركة نقل موجودة
                        _context!.TransportCompanies.Update(transportCompany);
                    }

                    await _context.SaveChangesAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حفظ شركة النقل");
                return false;
            }
        }

        /// <summary>
        /// حذف الكائن من قاعدة البيانات
        /// </summary>
        protected override async Task<bool> DeleteEntityFromDatabase(object entity)
        {
            try
            {
                if (entity is TransportCompany transportCompany && transportCompany.TransportCompanyId > 0)
                {
                    // التحقق من وجود مركبات مرتبطة بشركة النقل
                    var hasVehicles = await _context!.Vehicles
                        .AnyAsync(v => v.TransportCompanyId == transportCompany.TransportCompanyId);

                    if (hasVehicles)
                    {
                        XtraMessageBox.Show("لا يمكن حذف شركة النقل لأنها مرتبطة بمركبات", "خطأ في الحذف",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }

                    var result = XtraMessageBox.Show(
                        $"هل أنت متأكد من حذف شركة النقل '{transportCompany.Name}'؟",
                        "تأكيد الحذف",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        _context!.TransportCompanies.Remove(transportCompany);
                        await _context.SaveChangesAsync();
                        return true;
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                HandleError(ex, "حذف شركة النقل");
                return false;
            }
        }

        #endregion
    }
}
